## Report documentation 

This document contains information on all the reports that this project manages, with the goal of keeping all the information
in one place:

| Customer               | Report                           | Description of report                                                                                                                                                                                                                                            | Delivery Mechanism | Delivery Destination                                                                           | Frequency/ Time of day            | Link to relevant code                                                                                                                                                                                    | Notes                                                                                                                                                                        |
|------------------------|----------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|------------------------------------------------------------------------------------------------|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Fidelity               | Daily index constituents file    | See FidelityReports.md                                                                                                                                                                                                                                           | Internal SFTP      | fidelity_bitcoin_index                                                                         | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/fidelity/fidelity_constituents_report.py) | The implementation of the these reports its a bit messy, it makes many redundant API calls. This is partially due to changing requirements over the lifecycle of this report |
| Fidelity               | Proforma index constituents file | See FidelityReport.md                                                                                                                                                                                                                                            | Internal SFTP      | fidelity_bitcoin_index                                                                         | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/fidelity/fidelity_constituents_report.py) | This report only runs during the "proforma window" or about 2 weeks per quarter. Explained more in FidelityReports.md                                                        |
| Fidelity               | Daily Index Levels Report        | Produces 4 files in the format <file-prefix>-YYYY-MM-DD.csv.zip. These files all contain 1d levels for the relevant indexes, going back to 2015. More recently we started calculating "marketcap" for these as well, which is explained more in the separate doc | Internal SFTP      | fidelity_bitcoin_index                                                                         | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/fidelity/fidelity_index_exporters.py)     | These reports came from the SFTP service we use to run                                                                                                                       |
| Fidelity               | Realtime Index Levels Report     | Produces 4 files in the format <file-prefix>-YYYY-MM-DD.csv.zip. These files contain "realtime" (15s) index levels for the relevant indexes, starting from the previous day at 4PM until the current day at 4PM                                                  | Internal SFTP      | fidelity-bitcoin-index                                                                         | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/fidelity/fidelity_index_exporters.py)     | Same as above                                                                                                                                                                |
| Fidelity               | FIDBTC Close Monitor             | Sends an email shortly after close, checking our prices against external prices as well as providing some info from our database.                                                                                                                                | Email              | <EMAIL>                                                          | 4:04PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/monitor_reports/fidelity/check_close.py)  | These came from the monitor report                                                                                                                                           |
| Fidelity               | FIDETH Close Monitor             | Sends an email shortly after close, checking our prices against external prices as well as providing some info from our database.                                                                                                                                | Email              | <EMAIL>                                                         | 4:04PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/monitor_reports/fidelity/check_close.py)  | These came from the monitor report                                                                                                                                           |
| CMBI                   | CMBI ETH Report                  | Creates two files cmbi-eth-rt-YYYY-MM-DD.csv and cmbi-eth-close-YYYY-MM-DD.csv. Where the first contains data at a  15s interval for the day for CMBIETH and the second contains hourly data for CMBIETH for the day.                                            | Internal SFTP      | cmbi_export/eth                                                                                | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/cmbi_export/cmbi_indexes_export.py)       | These were originally part of our SFTP reporting                                                                                                                             |
| CMBI                   | CMBI BTC Report                  | Creates two files cmbi-btc-rt-YYYY-MM-DD.csv and cmbi-btc-close-YYYY-MM-DD.csv. Where the first contains data at a  15s interval for the day for CMBIBTC and the second contains hourly data for CMBIBTC for the day.                                            | Internal SFTP      | cmbi_export/btc                                                                                | 4:07PM Eastern Daily              | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/cmbi_export/cmbi_indexes_export.py)       | Same as above                                                                                                                                                                |
| CMBI Singapore?        | CMBI Singapore Close Report      | Sends an email containing information about CMBI Bitcoin Price                                                                                                                                                                                                   | Email              | <EMAIL>                                                            | 4:06PM Asia/Singapore Daily       | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/monitor_reports/cmbi/singapore_close.py)  | Initially came from monitor project, not sure who the actual customer is.                                                                                                    |
| CMBI London?           | CMBI London Close Report         | Sends an email with data from many CMBI indices                                                                                                                                                                                                                  | Email              | <EMAIL>                                                               | 4:06 PM Europe/London Daily       | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/monitor_reports/cmbi/london_close.py)     | Initially from monitor project                                                                                                                                               |
| GlobalX                | GlobalX Index Report             | Sends an email with data from many CMBI indices                                                                                                                                                                                                                  | Email              | <EMAIL>                                                                        | 4:06PM Europe/London Daily        | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/monitor_reports/cmbi/globalx_report.py)   | Initially from monitor project                                                                                                                                               | 
| DDA                    | DDA Momentum/ Rebalance Report   | Creates a file named cmbi_momentum_YYYY-MM-DD.csv or cmbi_rebalance_YYYY-MM-DD.csv. The file is momentum, except for on the first  NYSE trading day of the week it is rebalance.                                                                                 | Internal SFTP      | ddadata                                                                                        | 4:06 PM Europe/London Daily       | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/dda/dda_momentum_rebalance_reports.py)    |                                                                                                                                                                              |
| DDA                    | CMBI London Close                | Creates a file with closing prices for a basket of CMBI indexes                                                                                                                                                                                                  | Internal SFTP      | ddadata                                                                                        | 4:06PM Europe/London Daily        | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/dda/cmbi_london_close_report.py)          |                                                                                                                                                                              |
| Grayscale              | Grayscale Report                 | Creats a file CM_rates_NY.csv that has daily information for ~50 assets reference rates                                                                                                                                                                          | Email              | <EMAIL>, <EMAIL> as well as some other personal email addresses | 4:06PM Eastern                    | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/cmbi_export/grayscale_export.py)          |                                                                                                                                                                              |
| Castle Island Ventures | CIV EOM report                   | Creates a file reference_rates_<previous-month>.csv on the first of the month, that has reference rates for all previous months                                                                                                                                  | Email              | CIV emails                                                                                     | 7:00AM Eastern 1st of every month | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/last_day_of_month_rr_file.py)             | This report failed once due to poor API performance when querying many assets, may be worth tweaking slightly if it occurs again                                             |
| Atlas Fund             | Monthly Reference Rates Report   | Creats a file reference_rates_MM_YYYY.csv with all our reference rates for the month                                                                                                                                                                             | External SFTP      | external sftp creds                                                                            | 7:00AM Eastern 1st of every month | [Link](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/export_atlas_data.py)                     | In the past this file delivery has failed because the customers SFTP server is down, if this is the case try and resend, then contact customer.                              |
