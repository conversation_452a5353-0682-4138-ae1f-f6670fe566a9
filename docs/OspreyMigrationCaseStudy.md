## Osprey Report Migration Case Study
This document exists to show how a developer would add a new report to data pusher as of June 2023, so the process is 
clear and documented. I will try and log all code I run during this so the process is understandable and reproducible.

### Osprey report
[The Osprey report currently exists in the monitor project](https://gitlab.com/coinmetrics/market-data/monitor/-/blob/master/src/main/kotlin/io/coinmetrics/monitor/cmbi/CheckCloseOsprey.kt).
We need to convert it into Python and set it up in data pusher. This the last customer facing report outside of data pusher,
so when this is completed we will have consolidated all the other reporting systems. The way it works is it sends an
email with the with CMBIBTC and the CMBIETH, as well as data from binance.us, binance, coinbase, and <NAME_EMAIL>. 

#### Steps
1. I will implement this in python and try to get it to run locally. The general pattern used in the project is to make
a python script somewhere in the python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts folder
that creates the relevant email string. If we were sending a file to this customer the function would return the path
or paths to the relevant file(s), but since it's just an email body that's not relevant. 
1A. Looking at the report it seems that it calls the Kraken and Coinbase API's
2. Normally I find it easiest to just implement the report in python locally, test the output against what already exists, and
then do integration tests with the real application. At this point I'm assuming the user has followed the steps in "Set up"
in the main readme in order to develop locally
3. Since this report is being migrated, I'm going to first add a function step and a test, so we can verify that the new 
report matches old report. I navigate to google groups -> <EMAIL> in order to find an example of the existing report. Test is here:
```python
import datetime

import pytest
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.osprey import create_osprey_email

def test_osprey_report_2023_06_23() -> None:
    """
    This test checks that osprey created for 2023-06-23 with new python script match the old version generated by
    deprecated monitor project
    """
    report_date = datetime.date(year=2023, month=6, day=23)
    expected = open("files/osprey_report_2023_06_23.txt", 'r').read()
    actual = create_osprey_email(report_date=report_date)
    assert actual == expected


if __name__ == '__main__':
    pytest.main()
```
Now this test will fail obviously because we haven't actually implemented the function to create the email body. 
4. Now to actually implement the report itself requires some judgement on the part of the developer. I normally follow
the philosophy that whenever we can query our own API and use the Python API Client that is the best option since it makes
us customers of our own product, but in this case the [original report is querying data that is exclusive to the database](https://gitlab.com/coinmetrics/market-data/monitor/-/blob/master/src/main/kotlin/io/coinmetrics/monitor/cmbi/CheckCloseOsprey.kt),
so I will just copy over the queries that already create this report (there is some more code not shown here):
```python
import datetime
import os
import pytz
from coinmetrics.api_client import CoinMetricsClient
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.database_trade_reader import DatabaseTradeReader

client = CoinMetricsClient(os.environ.get('CM_API_KEY'))

class OspreyEmailReport:

    def __init__(self, report_date: datetime.date):
        self.report_date = report_date
        eastern_tz = pytz.timezone("America/New_York")
        self.report_time = eastern_tz.localize(datetime.datetime(year=report_date.year,
                                                   month=report_date.month,
                                                   day=report_date.day, hour=16))

    def get_formatted_report_time(self) -> str:
        formatted_dt = self.report_time.strftime('%Y-%m-%dT%H:%M:%S%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]
        return formatted_dt

    def get_4pm_est_query_parm(self) -> str:
        """
        Gets the report time as a time that queries the API easily
        """
        report_time_param = self.report_time.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
        return report_time_param

    def create_index_levels_string(self) -> str:
        """
        This function generates the portion of the email that looks like:
        CMBI Bitcoin (CMBIBTC) price is: 30864.6664006429
        CMBI Ethereum (CMBIETH) price is: 1901.74777323203
        """
        report_time = self.get_4pm_est_query_parm()
        cmbi_btc_price = client.get_index_levels(indexes="CMBIBTC", start_time=report_time, end_time=report_time, frequency="1d-ny-close").to_list()[-1]['level']
        cmbi_eth_price = client.get_index_levels(indexes="CMBIETH", start_time=report_time, end_time=report_time, frequency="1d-ny-close").to_list()[-1]['level']
        result_string = f"""
CMBI Bitcoin (CMBIBTC) price is: {cmbi_btc_price}
CMBI Ethereum (CMBIETH) price is: {cmbi_eth_price}
"""
        return result_string

    def create_exchange_levels_string(self) -> str:
        """
        Creates a string of prices from exchanges based on customer request
        """
        result = ""
        exchange_assets_dict = {
            "Binance.US": ["BNB", "HNT"],
            "Bitfinex": ["AVAX"],
            "Bittrex": ["CELO"],
            "Coinbase": ["BTC", "ETH", "ADA", "ALGO", "BAT", "DOT", "MATIC", "SOL", "AVAX", "CELO", "XTZ", "KSM"],
            "Kraken": ["BTC", "ADA", "ALGO", "BAT", "DOT", "MATIC", "SOL", "AVAX", "CELO"],
        }
        database_reader = DatabaseTradeReader(report_date=self.report_date)
        for exchange, assets in exchange_assets_dict.items():
            for asset in assets:
                exchange_asset_price = database_reader.read_close_trade(exchange=exchange, base=asset, quote=3)
                new_line = f"{exchange} {asset} {'--' if not exchange_asset_price else exchange_asset_price}"
                result += new_line + "\n"
            if exchange == "Coinbase":
                result += "\n\n\n"
            else:
                result += "\n"
        return result

    def generate_full_report(self):
        """
        Creates Osprey report for given date. This report came from monitor project and is sent to the user daily at 4pm est
        Example of file output can be found in ../../tests/files/osprey....
        :param report_date: date of the data for report. Data will come from 4pm on that day.
        :return: str txt formatted of the osprey email. Should be converted to html for customer.
        """
        result = ""

        report_header = f"""
All,

Please see below for a copy of today's closing levels.
Please feel free to reach out with any questions.
Thank you.

CMBI Index report for: {self.get_formatted_report_time()}[America/New_York]
{self.create_index_levels_string()}
Please feel free to reach <NAME_EMAIL> with any questions.

{self.create_exchange_levels_string()}
"""
        result += report_header

        return result
```
An issue I came across was that this report seems to not be able to get the historical values from the reports - 
the values are off by ~1% on many assets. This might be due to differences in staging and prod data or something else, 
but might be worth coming back to.

In the actual python code you'll notice it's just creating a raw string output, this is sufficient for creating an email.
Now we will want to integrate and test this locally.
6. I will add a ReportGenerator subclass to to the rpeort_generators.py file:
```python
class OspreyReportGenerator(ReportGenerator):
    
    def run_report(self) -> bool:
        report_date = self.get_report_date_4pm_est()
        if not report_date.weekday() <= 4:
            return False
        if not self.customer_report.delivery_method == "email":
            logging.warning(msg="Not running Osprey report because it is only eligible to sent over email"
                                f"customer report id: {self.customer_report.pk}")
            return False
        return True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()
        reporter = OspreyEmailReport(report_date=report_date)
        email_body = reporter.generate_full_report()
        email_html = self.paragraph_to_html(email_body)
        email_header = f"CMBI Index report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(result_files=None, customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_html
                             )
```
The main methods in all these report generators is generate_report - this is  so that we 
have the right logic for passing dates into the right reports and in this case the email headers. For each report we run
we basically wrap the main code in a ReportGenerator object. If you look at the code for ReportGenerator it standardizes
things like alerting, file backups, logging errors, etc.  

I also override the method run_report which adds some custom logic - in this case we want this report to only be ran via
email and also to not run on the weekends
7. Now I will update common.py which has a mapping of report generators to UI names
8. Not shown, in a separate file `database_readers.py` I added a new environment variable PG_TRADES_DATABASE, so I will 
update the environment variable configurations in helm. The name of the database is not a secret, but it is environment
dependent, so I pass it to values-cp1.yaml, values-cdev1.yaml, then to statefulset.yaml. I also add it to secret-values-local.yaml,
since for local we bunch secret and non secret values
9. Now I will test this locally. I start up my local data pusher env with `nix develop` and then `cd python` and 
`sops exec-env ../helm/secret-values-local.yaml "python manage.py runserver"`
10. Last step locally is to test this from the UI - I create an instance of the report in customer reports in the 
admin panel. I then select it and hit "Manually Trigger Report". I then <NAME_EMAIL> google
group to make sure that everything works as expected. Satisfied, I push to MR and deploy to staging where I repeat this process.
11. Confirming it works on staging, I create a release ticket and add to production. Once created in production, I will
manually test it again to ensure everyting works, before creating an instance of the report with the customers email. 