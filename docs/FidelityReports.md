## Fidelity Reports
​
The reports we send to fidelity have more logic than most of the rest of the reports, adding that to this document since
it can be hard to understand just the codebase.
​
### Constituent reports
​
The constituent reports send some calculations to fidelity at NY close every day. Important to understand:
​
* Proforma window - the proforma window begins 10 NYSE business days prior to the third Friday in March, June, September, and December.  This window ends on the last business day before the rebalance effective date.   During this window, we send an additional "proforma-constituents" file to fidelity. The most recent proforma window is June 2nd - June 15th for example. These dates were [calculated here](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts/fidelity/data_capture_dates_2.py).  During the proforma window, we use the "proforma date" as the data capture date.
​
* Data capture date - The data capture date is the business date prior to the start of the proforma window.  Effectively, it is the 11th business date prior to the rebalance effective date.  For example, in 2023 the first data capture date as March 2nd and the second is June 1st. We use the data capture date to as the date for several inputs to our calculations in these reports
​
* Marketcap - For indices it is calculated as (Splyff on data capture date) * price of the underlying asset price index
 
  * So if the "underlying asset" is BTC this would be SplyffBtc on data capture date * FIDBTCP on reporting date
  * For each multiasset index we add a column "asset ticker", which is either BTC or ETH and that is considered the underlying asset
​
* Weight - For single asset indices is "1", for multi asset indices we query the weight from the /timeseries/index-constituents
  on the reporting day. For proforma weight we calculate a "theoretical weight" this uses the formula (market cap underlying asset) / (combined market cap)
​
* Index shares - We use the formula weight * index price level / asset price index
 
  * So for FIDBEIP with underlying asset BTC this would be weight * (FIDBEIP Price Reporting Date) / (FIDBTCP Price Level)
​
* Unscaled Shares - For single asset indexes this is just SplyFF of underlying asset on data capture date. For multi asset indices, we
  calculate a "theoretical constituent weighted SplyFF", which is calculated with the formula .5 * (combined market cap eth and BTC / underlying asset at data capture date)
  Those are the key concepts for the fidelity reports. To clarify we send the fidelity-constituents-YYYY-MM-DD.csv every day at market close
  and we only send the proforma file during the proforma window. At the end of the proforma window we update the data capture date.
  So on June 15th 2023 the data capture date is March 17th 2023, then on June 16th the data capture date for the main report will be June 2nd until the next window.
 
  ## Daily Realtime and Overall Index Levels reports
 
  In addition to those above, we send fidelity "realtime" index levels files that has intraday prices, as well as daily index
  level files that have close prices going back to 2015. These files are mostly normal compared to the above reports, except
  they also contain a "marketcap" or "market_value" calculations, which are the same as above - SplyFF at data capture date * asset price.
  So it's important to make sure these also have an updated data capture date.
  On any given day during the proforma window, the total of reports sent to fidelity shoud look like below. When it's not
  proforma it will look the same except without the proforma file.
 
  ```commandline
  Jun 14 20:07 fidelity-constituents-2023-06-14.csv.zip
  Jun 14 20:07 fidbtc-2023-06-14.csv.zip
  Jun 14 20:07 fideth-2023-06-14.csv.zip
  Jun 14 20:07 fidebe-2023-06-14.csv.zip
  Jun 14 20:07 fidbei-2023-06-14.csv.zip
  Jun 14 20:07 proforma-constituents-2023-06-14.csv.zip
  Jun 14 20:07 fidbtc-2023-06-14T1600-0400.csv.zip
  Jun 14 20:07 fideth-2023-06-14T1600-0400.csv.zip
  Jun 14 20:07 fidebe-2023-06-14T1600-0400.csv.zip
  Jun 14 20:07 fidbei-2023-06-14T1600-0400.csv.zip
  ```

## Generating these reports locally
In case any fidelity reports are needed to be generated locally for some reason - either issue with production or local
testing, there is additional scripts to run them locally. These can be found at [run_reports_local](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/tree/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts).
Instructions for how to run these locally:
1. Using Pycharm, follow the [instructions in main readme to set up project](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher#configuring-project-for-local-development-of-python-scripts-in-pycharm)
2. Just run the __main__ script in the file that has the reports you wish to run. If you wish to run a report accessing 
a database, such as the fidelity monitor reports, you will also need to portforward to the staging database. This can be
`done with the command kubectl -n pgbouncer port-forward svc/pgbouncer 5432:5432 --context=cdev1