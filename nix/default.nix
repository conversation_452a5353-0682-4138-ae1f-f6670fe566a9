{ buildPythonPackage, celery, ciso8601, coinmetrics-api-client, typer, django_4
, django-cryptography, django-celery-beat, django-prometheus, django-encrypted-model-fields
, os-utils, pyftpclient, pysftp, python, redis, sendgrid, whitenoise, httpie, psycopg2, minio, croniter }:
buildPythonPackage rec {
  pname = "cm-data-pusher";
  version = "0.1.0";
  format = "pyproject";

  src = ../python;

  outputs = [ "out" "static" ];

  propagatedBuildInputs = [
    celery ciso8601 coinmetrics-api-client django_4 django-celery-beat django-prometheus typer
    django-cryptography django-encrypted-model-fields os-utils pyftpclient
    pysftp redis sendgrid whitenoise httpie psycopg2 minio croniter
  ] ++ coinmetrics-api-client.optional-dependencies.pandas;

  pythonCheckImports = [ "coinmetrics_data_pusher" ];

  postBuild = ''
    mkdir -p $out/lib/python3.11/site-packages/coinmetrics_data_pusher/customer_reporting/templates
    cp -r ./coinmetrics_data_pusher/customer_reporting/templates/* $out/lib/python3.11/site-packages/coinmetrics_data_pusher/customer_reporting/templates/

    mkdir -p $static

    export CM_DATA_PUSHER_STATIC_ROOT=$static
    ${python.interpreter} ./manage.py collectstatic
  '';

  meta = {
    description = "Coin Metrics Data Pusher";
    homepage = "https://gitlab.com/coinmetrics/data-delivery/export/data-pusher";
  };
}
