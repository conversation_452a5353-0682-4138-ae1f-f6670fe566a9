{ fetchFromGitHub, gnused, image, kubernetes-helm, runCommandLocal, skopeo
, sops, writeShellApplication }:
rec {
  registryLoginScript = writeShellApplication {
    name = "authenticate-to-registry";
    runtimeInputs = [ skopeo ];
    text = ''
      skopeo login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY" <<< "$CI_REGISTRY_PASSWORD"
    '';
  };

  publishImage = writeShellApplication {
    name = "publish-image";
    runtimeInputs = [ skopeo ];
    text = ''
      skopeo --insecure-policy copy -f oci docker-archive:${image} docker://"$CI_REGISTRY_IMAGE":"$CI_COMMIT_SHORT_SHA"
    '';
  };

  helmSecrets = fetchFromGitHub {
    owner = "jkroepke";
    repo = "helm-secrets";
    rev = "v3.15.0";
    hash = "sha256-yZfYsDFZK34BfsfLzfwKC+VOmc9In8IazlSzUxdBmx4=";
  };

  helmPluginsEnv = runCommandLocal "helm-plugins" { } ''
    mkdir -p $out
    ln -s ${helmSecrets} $out/helm-secrets
  '';

  deployApplication = writeShellApplication {
    name = "deploy-application";

    runtimeInputs = [
      gnused
      kubernetes-helm
      sops
    ];

    text = ''
      set -x
      export HELM_PLUGINS=${helmPluginsEnv}
      cd ./helm
      helm -n data-pusher upgrade --install data-pusher chart -f values-"$1".yaml -f secrets://secret-values-"$1".yaml --set image.tag="$CI_COMMIT_SHORT_SHA"
    '';
  };
}
