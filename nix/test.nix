{ buildEnv
, cm-data-pusher
, python
, sops
, writeShellApplication
}:
let
  env = buildEnv {
    name = "cm-data-pusher-env";
    paths = [
      (python.withPackages (pkgs: with pkgs; [
        cm-data-pusher
        django
      ]))
      sops
    ];
  };
in {
  integrationTests = writeShellApplication {
    name = "integration-tests";

    runtimeInputs = [
      env
    ];

    text = ''
      export DJANGO_SETTINGS_MODULE=coinmetrics_data_pusher.settings
      sops exec-env ${../helm/secret-values-test.yaml} "django-admin test --pythonpath ${cm-data-pusher}/lib/${python.libPrefix}/site-packages coinmetrics_data_pusher.customer_reporting"
    '';
  };
}
