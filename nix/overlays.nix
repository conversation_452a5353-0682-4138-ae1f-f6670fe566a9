{
  all = final: prev: {
    python311 = prev.python311.override (old: {
      packageOverrides = prev.lib.composeExtensions (old.packageOverrides or (_: _: { })) (pfinal: pprev: {
        # Disable upstream pytest for cfn-lint (avoids W2531 exit code failure)
        "cfn-lint" = pprev."cfn-lint".overridePythonAttrs (_: {
          doCheck = false;
        });
        os-utils = pfinal.callPackage (
          { buildPythonPackage, fetchPypi }:
            buildPythonPackage rec {
              pname = "os_utils";
              version = "0.1.6";

              src = pprev.fetchPypi {
                inherit pname version;
                hash = "sha256-toGqEkWq/VolnGwiJDxVEjmdAIb4is7hNZQX6aFThG4=";
              };
            }
        ) { };

        pyftpclient = pfinal.callPackage (
          { buildPythonPackage, fetchPypi }:
            buildPythonPackage rec {
              pname = "pyftpclient";
              version = "0.1.15";

              src = pprev.fetchPypi {
                inherit pname version;
                hash = "sha256-fWbM1PAuNQCRx8eOHuD4UwLtGGEIeWxLCKQub2QECmU=";
              };

              propagatedBuildInputs = [ pfinal.os-utils pprev.paramiko ];
            }
        ) { };
      });
    });
  };

  darwin = final: prev: { };
}
