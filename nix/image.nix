{ buildEnv, cm-data-pusher, dockerTools, python311, writeShellApplication }:
let
  startCeleryBeat = writeShellApplication {
    name = "start-celery-beat";

    text = ''
      celery beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler --logfile /dev/stdout
    '';
  };

  startCeleryWorker = writeShellApplication {
    name = "start-celery-worker";

    text = ''
      celery worker -l info --logfile /dev/stdout --concurrency 4 --prefetch-multiplier 1
    '';
  };

  startGunicorn = writeShellApplication {
    name = "start-gunicorn";

    text = ''
      gunicorn --bind '[::]:8000' --workers 3 --timeout 600 coinmetrics_data_pusher.wsgi "$@"
    '';
  };

  runTests = writeShellApplication {
    name = "run-tests";

    text = ''
      ${python311.interpreter} python/manage.py test
    '';
  };

  env = buildEnv {
    name = "cm-data-pusher-env";
    paths = [
      (python311.withPackages (pkgs: with pkgs; [
        celery
        cm-data-pusher
        django
        gunicorn
        httpie
      ]))
      startCeleryBeat
      startCeleryWorker
      startGunicorn
      runTests
    ];
  };
in
dockerTools.buildLayeredImage {
  name = "coinmetrics/cm-data-pusher";
  tag = "latest";
  maxLayers = 4;
  created = "now";

  contents = [
    dockerTools.caCertificates
  ];

  fakeRootCommands = ''
    mkdir tmp
    chmod 777 tmp
  '';

  config = {
    Entrypoint = [ "start-gunicorn" ];
    User = "1000:1000";
    Env = [
      "CELERY_APP=coinmetrics_data_pusher"
      "CM_DATA_PUSHER_STATIC_ROOT=${cm-data-pusher.static}"
      "DJANGO_SETTINGS_MODULE=coinmetrics_data_pusher.settings"
      "PATH=${env}/bin"
    ];
  };
}
