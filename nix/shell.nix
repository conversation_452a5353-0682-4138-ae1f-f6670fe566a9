{ cm-data-pusher, mkShell, python311Packages, redis, writeShellScriptBin }:
let
  workDir = "/tmp/cm-data-pusher";
in mkShell {
  packages = [
    python311Packages.gunicorn
    python311Packages.supervisor
    python311Packages.pip
    redis
  ];

  inputsFrom = [
    cm-data-pusher
  ];

  shellHook = ''
    export src=$(realpath .)
    export workDir=${workDir}
    export CM_DATA_PUSHER_DEBUG=1
    export CM_DATA_PUSHER_DB=$workDir/db.sqlite
    export CM_DATA_PUSHER_CELERY_BROKER_URL=redis+socket://$workDir/redis.sock
    export CELERY_APP=coinmetrics_data_pusher
    export DJANGO_SETTINGS_MODULE=coinmetrics_data_pusher.settings
    export PYTHONPATH=$src/python:$PYTHONPATH

    alias start="supervisord -c ./nix/config/supervisord.conf"
    alias stop="supervisorctl -c ./nix/config/supervisord.conf shutdown"
    alias status="supervisorctl -c ./nix/config/supervisord.conf status"

    mkdir -p $workDir
    [[ -f ./db.sqlite ]] && cp ./db.sqlite $workDir/db.sqlite
  '';
}
