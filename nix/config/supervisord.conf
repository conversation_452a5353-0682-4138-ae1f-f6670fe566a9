[unix_http_server]
file = %(ENV_workDir)s/supervisord.sock
chmod = 0600

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon = false
logfile = %(ENV_workDir)s/supervisord.log
pidfile = %(ENV_workDir)s/supervisord.pid

[supervisorctl]
serverurl = unix://%(ENV_workDir)s/supervisord.sock

[program:redis]
directory = %(ENV_workDir)s
command = redis-server %(ENV_src)s/nix/config/redis-dev.conf
stdout_logfile = %(ENV_workDir)s/redis.log
redirect_stderr = true

[program:celery-worker]
directory = %(ENV_src)s/python
command = celery worker --loglevel=INFO
stdout_logfile = %(ENV_workDir)s/celery-worker.log
redirect_stderr = true

[program:celery-beat]
directory = %(ENV_src)s/python
command = celery beat --loglevel=INFO --scheduler django_celery_beat.schedulers:DatabaseScheduler
stdout_logfile = %(ENV_workDir)s/celery-beat.log
redirect_stderr = true
