#!/bin/bash

echo "Starting development environment"
if ! supervisord -c ./nix/config/supervisord.conf > supervisord_output.txt 2>&1; then
    if grep -q "Error: Another program is already listening on a port that one of our HTTP servers is configured to use.  Shut this program down first before starting supervisord." supervisord_output.txt; then
        cat supervisord_output.txt
        echo "Error: supervisord is already listening, continuing..."
    else
        cat supervisord_output.txt
        echo "Unknown error occurred."
        exit 1
    fi
fi

echo "Entering python directory"
cd python/ || exit

echo "Making migrations"
python manage.py makemigrations || exit

echo "Migrating"
python manage.py migrate || exit

echo "Collecting static files"
python manage.py collectstatic --noinput

echo "Setting environment variables"
export DJANGO_SUPERUSER_USERNAME=admin
export DJANGO_SUPERUSER_PASSWORD=admin
export DJANGO_SUPERUSER_EMAIL=<EMAIL>

echo "Creating a user, username: $DJANGO_SUPERUSER_USERNAME, password: $DJANGO_SUPERUSER_PASSWORD, email: $DJANGO_SUPERUSER_EMAIL"
if ! python manage.py createsuperuser --noinput > createsuperuser_output.txt 2>&1; then
    if grep -q "CommandError: Error: That username is already taken." createsuperuser_output.txt; then
        cat createsuperuser_output.txt
        echo "Error: Username already taken, continuing..."
    else
        cat createsuperuser_output.txt
        echo "Unknown error occurred."
        exit 1
    fi
fi

echo "Starting a server"
sops exec-env ../helm/secret-values-local.yaml "python manage.py runserver" || exit
