## DataPusher - What is this repo for?
This project is an internal app made to automate running reports and sending them to customers both externally and 
internally. The goal of this project is to deprecate a lot of the existing, separete services we run internally to send 
data to customers, so there is one place to keep data export scripts, monitoring, etc. Over time, improvements in reliability 
and features will allow us to provide better reporting across the board. It leverages [Django](https://www.djangoproject.com/) for the backend/ frontend of the app, [Celery](https://docs.celeryq.dev/en/stable/getting-started/introduction.html)
to manage running tasks, and is deployed on our Kubernetes cluster using [NixOS](https://nixos.org/). 


### Customer reports 
See the file docs/ReportDocs.md for specific info on the reporting obligations this project takes care of. Please 
keep it up to date as things change. 

### Where are customer reports configured and enabled?
In this repo the logic to generate reports are written in Python, leveraging our API Client wherever possible, and the
are instantiated/ managed using a Django frontend. For the moment, we leverage the built in django admin panel, but plan to
build some custom configurations as well for monitoring. The urls to access these are below, and credentials can be requested.

Customer reports page staging: https://data-pusher-stg.cnmtrcs.io/admin/customer_reporting/customerreport/
Customer reports page production: https://data-pusher.cnmtrcs.io/admin/customer_reporting/customerreport/

### Google SSO and Customizing the admin panel
We use the out of the box [Django admin panel](https://docs.djangoproject.com/en/4.2/ref/contrib/admin/) to configure 
and sometimes "manually trigger" reports if they have failed or need to be spot tested for some reason. We have added 
some custom functionality to the admin panel, this is done in [admin.py](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/admin.py?ref_type=heads).
So in general, you were going to add some custom functionality to the front end, that would be the place to do it. This is 
also how we have Google SSO enabled. In that `admin.py` file we define a `CustomAdminSite` object that overrides defualt log 
in behavior - if the user is not already authorized it will redirect them to a google auth page that requires a CM login.
The OAuth credentials and configuration in GCP by people with security admin access at CM (at the time or writing this was David A),
if new credentials or configurations are needed you would need to go through them. OAuth methods were added manually in [views.py](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/views.py?ref_type=heads)
rather than using some of the out of the box solutions available online liek django-allauth. I tried some of these methods
and they were too general for our use case and in addition add a new dependency we didn't really need. 

### Creating customer reports/ development process
Currently this tool can send reports to customer locations at external SFTP (via password auth), our internal SFTP
(it has an SSH Private Key auth'd to out internal SFTP server as a CI/CD Build arg), and by email (there is sendgrid 
credentials as a build arg as well).

The general development process is to push changes to an MR branch, deploy to staging and run tests, then open a release ticket 
to deploy to production. A good example of this in practice is this MR which adds the [BNYM index report](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/5/diffs).
At the minimum, each new test should have an integration test added to the customer_reporting/tests/test_integrations_tests_reports.py
file before it is moved to production. 

#### Running integration tests locally

Run this:
```commandline
export NIX_CONFIG="access-tokens = gitlab.com=PAT:<TOKEN>"
```
where <TOKEN> is your personal access token.

Then:
```commandline
nix run .#integrationTests
```

### Repo explanation
This repo uses NixOS which is probably unfamiliar to many users, [this article](https://serokell.io/blog/what-is-nix)
may be helpful to understand the pros of using nix. At the moment this repo will be the first in CM to use NixOS with 
the hope of it being an example to use elsewhere. Big picture, we are using NixOS in order to build reproducible
container images, similar to docker images but hopefully will create a better development and production experience. Files:

#### default.nix
This is the file used by default with nix-build, kind of like a Dockerfile. It contains all the python dependencies 
we need to run the application in `propagatedBuildInputs`. This looks towards [nixpkgs](https://nixos.wiki/wiki/Nixpkgs)
the main package repository for NixOS packages. It can be thought of as similar to `apt` in linux systems or `brew` on 
mac. If we wish to use a depdendency that is not already a part of `nixpkgs` then we might need to [package them 
manually](https://nix-tutorial.gitlabpages.inria.fr/nix-tutorial/first-package.html). 

#### flake.nix
THe `flake.nix` file contains all the inputs and outputs that go into the building our app, using this allows the build 
to be reproducible bit-by-bit, to a much higher degree than docker. It conains commands in the `apps` variable that are
key to running and deploying the app. `cm-data-pusher` runs the app, `login`, `publish`, and `deploy` are used as part
the CI/CD process. [Wiki page on flakes](https://nixos.wiki/wiki/Flakes).

#### Image.nix 
[Article on building container images with Nix vs. Dockerfiles](https://thewagner.net/blog/2021/02/25/building-container-images-with-nix/).
The `image.nix` file is where we specify the images we want to layer on. It uses a [union file system](https://en.wikipedia.org/wiki/UnionFS)
so multiple file systems can be stacked on top of eachother. It also has an `entrypoint` feature that works similar to an
entrypoint in Docker.

#### Helm
> Helm is a Kubernetes deployment tool for automating creation, packaging, configuration, and deployment of applications and services to Kubernetes clusters.
- [From What is helm?](Helm is a Kubernetes deployment tool for automating creation, packaging, configuration, and deployment of applications and services to Kubernetes clusters.)
This folder contains all the deployment configurations for running with kubernetes. It leverages [kubernetes stateful sets](https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/)
that help run stateful application, like this one. 

## Developing

### Set up
* Follow instructions from the [CM Kubernetes wiki](https://gitlab.com/coinmetrics/wiki/-/wikis/Kubernetes/Introduction-to-k8s-at-CM#prerequisites-to-install-on-your-local-workstation)
* Install [sops](https://formulae.brew.sh/formula/sops) - tool to manage secrets
* Install [rage](https://github.com/str4d/rage#installation) - Rust implementation of encryption format [AGE](https://github.com/C2SP/C2SP/blob/main/age.md)
can be though of as a simpler alternative to GPG encryption
  * To use this in practice you will need to get the `SOPS_AGE_KEY` from the Settings -> CI/CD -> Variables and copy the value
to a file at `~/Library/Application\ Support/sops/age/keys.txt` (assuming you are using mac)
* Recommended to have Python 3.10 or greater installed locally

#### Configuring project for local development of Python scripts in Pycharm
Since this project uses Nix the package structure is different than a normal python project - it is kind of a monorepo.
These steps will let a developer use PyCharm to develop the application locally without errors:

1. Clone the repo <NAME_EMAIL>:coinmetrics/data-delivery/export/data-pusher.git
2. Open 'data-pusher' in Pycharm
3. Go to Preferences -> Project: data-pusher -> Project structure
4. Remove default project root, then add python, helm, nix, and docs as project roots 
5. (Optional) Preferences -> Project: data-pusher -> Project interpreter -> add interpreter in data-pusher/python
6. With virtual environment activated run `pip install -r requirements.txt`. This step can require debugging depending on
what version of linux/ macos/ windows you are using
7. Port forwarding steps below may be relevant if you need to access a database or other service over kubernetes

At this point if you are using PyCharm you should be able to run scripts located in [coinmetrics_api_scripts](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/tree/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts).
Locally.


Most of the application code for data pusher is just Python scripts that query data from our API or databases in a 
specific format. Most of this can be done separately from running the whole data pusher project and iteration can be 
faster locally. Additionally, these steps will 

##### Keeping requirements.txt in sync with Nix dependencies

Over time, the Nix dependency versions may change, so it's important to keep them synchronized with requirements.txt to ensure accurate script testing.

Please run the following commands to update the file:
```commandline
nix develop
pip freeze > python/requirements.txt
```

#### Local development environment full application
For local developing we will be using an environment created with the command `nix develop` which creates an environment 
that can run this project locally, it can be thought of as similar to a Python Virtual environment - it creates a shell
within your shell made to run this program. To launch the local env in a way that will mirror staging or prod very closely
you can run:
1. Add your GitLab token to `.config/nix/nix.conf`:
```
access-tokens = gitlab.com=PAT:glpat-ABCDEFGHIJKL
```
2. `nix develop`
3. `start`
4. `cd python/`
5. `python manage.py makemigrations`
6. `python manage.py migrate`
7. `python manage.py createsuperuser`
8. `sops exec-env ../helm/secret-values-local.yaml "python manage.py runserver"`

Alternatively, you can just run the script from the root project directory:
```commandline
./start.sh
```
This script also creates a superuser: admin/admin.

##### Port forwarding
In order to access database or other services on kubernetes (i.e. there is a report that leverages candle-maker) you will
want to [follow standard instructions at cm](https://gitlab.com/coinmetrics/wiki/-/wikis/kubernetes/Useful-Kubernetes-commands#how-to-access-postgres-inside-of-kubernetes).
This application accesses that database at port 5432, so to access database I would port forward with the command: 
`kubectl -n pgbouncer port-forward svc/pgbouncer 5432:5432`. This would be helpful for running/ verifying some of the 
fidelity monitor reports for example which query a database.


#### Updating the codebase (non database migration) 
Steps to develop local changes and push those changes to Kubernetes staging cluster (not running in prod yet)
* Make changes in your local branch
* Run/ test locally using `nix develop`
* Commit, push, and merge changes after review
* Run publish and deploy CI jobs from the gitlab repo
* Delete the pod running the app, which will force it to restart with the new update
  * `kubectl -n data-pusher delete pod data-pusher-0`

#### Creating a New Database in the New Cluster

During the migration to the new cluster, you might need to create an empty database.

1. Deploy Data Pusher. It will be in a failed state but will create a persistent volume claim (PVC).
2. Uninstall the Helm chart:
```commandline
helm --kube-context cp1 uninstall --debug -n data-pusher data-pusher
```
3. Run the debug pod with the new PVC:
```commandline
kubectl --context cp1 apply -f bash-console.yaml
```
```
apiVersion: v1
kind: Pod
metadata:
  name: bash-console
  namespace: data-pusher
spec:
  containers:
  - name: bash
    image: ubuntu
    command: ["/bin/bash", "-c", "while true; do sleep 3600; done"]
    volumeMounts:
    - mountPath: /mnt/state
      name: state
  volumes:
  - name: state
    persistentVolumeClaim:
      claimName: state-data-pusher-0
  restartPolicy: Never
```
4. Exec into the debug pod:
```commandline
kubectl --context cp1 -n data-pusher exec -it bash-console -- bash
```
5. Ensure that the debug pod has access to the Internet to install sqlite3, e.g. configure the allow-all network policy.
```commandline
apt-get update
apt-get install sqlite3
```
6. Create an empty database and a test table:
```commandline
root@bash-console:/mnt/state# sqlite3 db.sqlite
SQLite version 3.45.1 2024-01-30 16:01:20
Enter ".help" for usage hints.
sqlite> create table tbl1(one text, two int);
sqlite> select * from tbl1;
sqlite> insert into tbl1 values('hello!',10);
sqlite> select * from tbl1;
hello!|10
```
7. Assign the correct rights because Data Pusher runs as user 1000:
```commandline
root@bash-console:/mnt# chown -R 1000:1000 state
root@bash-console:/mnt# chmod -R 0750 state
```
8. Delete the debug pod:
```commandline
kubectl --context cp1 -n data-pusher delete pods bash-console
```
9. Deploy Data Pusher.
10. Run the database migration:
```commandline
kubectl --context cp1 -n data-pusher exec -it data-pusher-0 -c data-pusher -- django-admin migrate
```

#### Updating the codebase and performing a database migration
This repeats most of the steps above with some slight changes: 
* Make changes in your local branch
* Run/ test locally using `nix develop`
* Run migration locally to confirm it works properly. This can be done with `python manage.py makemigrations` and then 
`python manage.py migrate`
* Commit, push, and merge changes after review - including the migration file, that is generally in `customer_reporting/migrations`
* Run publish and deploy CI jobs from the gitlab repo
* Run `kubectl -n data-pusher exec -it data-pusher-0 -c data-pusher -- django-admin migrate` (this cmd is still tbd) 
* Delete the pod running the app, which will force it to restart with the new update
* `kubectl -n data-pusher delete pod data-pusher-0`

### Deployment 
This app expects environment variables to be set in order to run properly. These are mainly configuration settings 
that might change based on environment such as `DEBUG` but also some sensitive values like API credentials, here is a complete list:
* `CM_DATA_PUSHER_DEBUG` - sets whether or not the Django server will run in DEBUG mode, which it shouldn't in production
* `CM_DATA_PUSHER_ALLOWED_HOSTS` - allowed hosts for the django application, defaults to "*"
* `CM_DATA_PUSHER_CRSF_TRUSTED_ORIGINS` - A list of trusted origins for unsafe requests i.e. post requests
* `CM_DATA_PUSHER_SENDGRID_EMAIL` - Email to be used for sendgrid emails, when emails are sent this is the email that will send them
* `CM_DATA_PUSHER_SENDGRID_API_KEY` - API Key associated with Sengrid account for the sepcified email 

### Running the app locally and debugging 
* To start developing, switch to your desired git branch and then run `nix develop`
* There are three aliases that are helpful to use in develop
  * `start` - this will start the `celery-beat` process(task scheduler), `celery-worker` process (workers to execute the 
  code for tasks), and the `redis` process (used as a message broker for celery)
  * `stop` - will shutdown the aforementioned tasks
  * `status` will give info on the state of the current tasks. If the tasks are not running it will indicate an error 
message generally, and if they are running the output should look something like:
```commandline
Ians-MBP:data-pusher ianmclaughlin$ status
celery-beat                      RUNNING   pid 37368, uptime 0:45:46
celery-worker                    RUNNING   pid 37369, uptime 0:45:46
redis                            RUNNING   pid 37370, uptime 0:45:46
```
* In order to run the Django app:
  * The basic way to run it is navigate to the `python` folder and run `python manage.py runserver`
  * This will not be fully featured since some aspects of the application rely on secrets being present in the environment
    * Currently these features are sending files to the CM internal SFTP and also sending emails
    * To run the application with these features you need to have `sops` installed, you need to have the RAGE key stored

### Updating Python Dependencies
Python pip dependencies are manage in `default.nix`. In order to add a new dependency you will want to add it to the
two build inputs lists. This is [done in this example](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/15)
when we decided to add `httpie` to this project. Additionally, we add it to `paths` in `image.nix`, so that it can be accessed from the commandline. 

#### Debugging Locally
* The best way to debug is looking at the various areas for log files
* On your local machine these files are stored in `/tmp/cm-data-pusher/...`
* The log files that are useful are mainly `celery-worker.log`, `celery-beat.log` but some of the other files there may
be of interest depending on the issue at hand
* The local db can be found at `db.sqlite` and can be queried to diagnose some issues

#### Debugging Staging/ Prod
* The best way to debug issues with the prod environment it also looking a the log files
* There is three main containers running in staging/ prod: `celery-beat`, `celery-worker`, `data-pusher`
  * These can be queried with:
    * `kubectl -n data-pusher logs -f data-pusher-0 -c <container-name>`


## CP1 maintenance or failover to CDEV1
For the CP1 maintenence period we will want to run this application in staging. We can effectively run the application 
in staging with a few steps:
1. Manually trigger the job in the data-pusher frontend "database backup" to ensure that there is a current version of
the database that is backed up.
2. Set the replicaCountDataPush -> 0 in value-cp1.yaml.
3. Set the variables dataPusherCookieSecretKeyOverride dataPusherCookieFieldEncryptionKeyOverride to the production, non
override values
3. Commit this and on Gitlab CICD run `publish` and `deploy_staging`
4. Ensure that there are no containers running for the service on CP1 at this point: `kubectl delete pod data-pusher-0 -n data-pusher`
5. Launch a python shell on staging with kubectl -n data-pusher exec -it data-pusher-0 -c celery-worker -- python --context=cdev1
6. Run these commands `from coinmetrics_data_pusher.customer_reporting.utility_scripts.failover_restore_db import replace_current_db_with_backup`, `replace_current_db_with_backup("PRODUCTION")`
7. At this point it should work and you can verify this by checking UI, if it doesn't work the most likely culprit is that the file permissions are not set correctly, double check they are permissive enough 


### Undoing this - turning staging back to staging 
1. Download the last staging backup from minio
2. Follow all the same steps - launch the alpine editor, replace db file, chmod the file
3. Edit `secret-value-cdev1.yaml` `dataPusherSecretCookieKey` and `dataPusherFieldEncryptionKey` back to "NA". We use the 
string "NA" in place of "" because helm/ sops does not allow for "" value to be resolved. The actual place these variables
are [used in the code is in settings.py](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/settings.py#L20).
4. Comment out alpine editor, run the application again as normal again 
5. Re-enable CP1 data pusher by setting replicaCountDataPush and replicaCountRedis -> "1" in values-cp1

I suggest this be done the Thursday or Friday before CP1 maintance. This process will run the application 1 to 1 as if it is in production. Alternatively, you can just configure the reports 
on the UI to match those in production and the applicaiton will run as normal. This is a bit more error prone potentially but is 
an option. This process can be used either to restore the application from backup or to do failover as we have coming up.
Updating to report this process worked without error for the CP1 maintanence. It was done with [this MR](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/42),
so if needed this application can run in production on cdev1, but under normal circumstances that should not be the case. 

## Bloomberg SFTP

For Bloomberg SFTP, we have an RSA private key:

https://gitlab.com/coinmetrics/ops/operations/-/tree/master/roles/fidelity_export/files/client?ref_type=heads

To use it in Data Pusher it should be transformed: 
```commandline
ssh-keygen -p -m PEM -f coinmetrics2bloomberg
```

Then encode it:
```commandline
base64 coinmetrics2bloomberg
```

And save the output to secret files for each environment.
