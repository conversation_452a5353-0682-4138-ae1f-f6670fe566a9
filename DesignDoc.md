# Data Pusher Design Document 
This document is intended to go deeper into the technical implementation of this project so it can be better understood
and can be used to inform the release process and review changes. 


## Application Code Explanation 
At a high level this project is a scheduler that schedules various data delivery tasks so that they can query the 
Coin Metrics API Client and other data sources and send them to our clients in the way that suits them. It provides a 
very simple admin front end that currently developers use to configure reports to customers.  

The application code of this project is fully in Python, leveraging ~4 large Python libraries as the main dependencies.
These Python libraries are [Django](https://pypi.org/project/Django/), [celery](https://pypi.org/project/celery/), 
[gunicorn](https://gunicorn.org/), and the [Coin Metrics API Client](https://github.com/coinmetrics/api-client-python).

### Brief Description of what these libraries are and why they were chosen for this project

**Django** - `Django is a high-level Python web framework that encourages rapid development and clean, pragmatic design.`.
We chose to use Django because it provides an easy to use ORM and admin interface for the application, and it is easy 
to add simple front end extensions. In the Python world it is seen as a way to not have to worry about a lot of the 
smaller details and just make simple, working front end back end applications. There is many options for this use case
but Django is probably the most popular and is well documented. 

**Celery** - Celery `Celery is a task queue implementation for Python web applications used to asynchronously execute work outside the HTTP request-response cycle.`
([source](https://www.fullstackpython.com/celery.html)). This project needs to implement a task queue because it is a 
scheduler, and celery is a standard well developed project for Python for this use case. In addition, there is an integration
between Django and celery called [django-celery-beat](https://django-celery-beat.readthedocs.io/en/latest/) that 
implements scheduling tasks within Django, so these libraries get us most of the way to our finished state without 
having to worry about some of this. Like other task queue's Celery interacts with a message broker to pass messages,
we decided to use Redis for this purpose, there is more detail on that in the Nix + K8s section.

**Gunicorn** - `Gunicorn 'Green Unicorn' is a Python WSGI HTTP Server for UNIX. It's a pre-fork worker model. The Gunicorn server is broadly compatible with various web frameworks, simply implemented, light on server resources, and fairly speedy.`
([source](https://gunicorn.org/)). We need to interface with the CM nginx server for this project to work. Gunicorn
is used for this use case in order to run workers to execute celery tasks and to send webpages/ data to nginx. Again,
this is the standard well documented option for Python HTTP servers. More details on configuration in the Nix + K8s 
section.

## Core application concepts - Database models, report generators, celery tasks, admin interface
This section explains the main parts of this project. This can be used to reference changes to the Python code generally.

### Database models 
The database tables are generated by Django using the ORM. All these tables are in [models.py](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/models.py).
The existing tables are documented there. Probably the two tables were noting are `Customer` and `CustomerReport`. 
`Customer` stores information on how to reach the right people via SFTP or otherwise and the `CustomerReport` objects 
are how we send customers information. Information in this file is not likely to change on a regualar basis. There is a 
process on how to run db migrations documented in the main README.md

### Report Generators
Theere is a concept of [report generators](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/report_generators.py) 
in this project. Basically the class `ReprotGenerator` defined at the top of that file is an abstract class that contains
all the logic for generic reporting - error handling, logging success or failure to DB, holding all the relevant information,
then the subclasses just implement the code to generate files for specific customer reports. For example, the 
class `CMBIExportETHFiles` will contain the logic for the CMBI Export ETH Files we send. The code in
`generate_report` is supposed to be just a main function, so the queries can easily be tested indepdently of this whole
project. Those are all imported from [coinmetrics_api_scripts](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/tree/master/python/coinmetrics_data_pusher/customer_reporting/coinmetrics_api_scripts).

### Celery tasks 
As mentioned before this project is executing celery tasks on a schedule. This project really just executes the same 
task for every customer in [tasks.py](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/tasks.py).
This uses the ReportGenerator class and DB models above to figure out what queries to runa and where to send them. 

### Admin interface
The main way we interact with this application is through the admin interface. The production version of this is at 
https://data-pusher.cnmtrcs.io/admin/, which is sealed by credentials. This is mostly out of the box django, but there 
is some additional functionality like adding a button to "Manually trigger reports" and custom froms in the [admin.py file](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/python/coinmetrics_data_pusher/customer_reporting/admin.py).

## Putting all those concepts together
We develop custom reports based on client requests as report generators. After developing, we deploy changes. Using the
admin interface we configure a schedule for the reports to be generated at, and then it runs in perpetuity. A concrete example of this is once we have 
sufficiently developed a report like the Fidelity Reports. There are several reports, but take FidelityConstituentsReport 
which needs to be sent out every day to an internal SFTP location. Once the report is developed we submit a form in the 
admin interface that says "Send to /fidelity-bitcoin-index location in SFTP every day at 4:05 PM". That is the whole 
development loop.


## Nix + K8s configuration
This project uses Nix to build docker images which are deployed on K8s. The reasoing for the various files we have in the 
nix folder are described in the README.md. For the sake of reviewing changes to this config it's important to note some
key ideas. The pattern this project is deployed with is somewhat new to CM, but is intended to become more common, so 
this section will focus mainly on how it applies to this application, not the general philosphy of this approach. Some of
that is described in the main README.md however. 

### image.nix
[This file](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/blob/master/nix/image.nix) contains the 
high level information for the image that is constructed. Important to note here that it defines three important shell
scripts that make this project work (`start-celery-beat`, `start-celery-worker`, `start-gunicorn`) and also what nix pkgs 
are available to the shell. [For example, here is an MR](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/15) where we added `httpie`, so that we can run http queries from the
k8s pods with commands like ` k -n data-pusher exec -it data-pusher-0 -c celery-worker -- https google.com`. In order to 
test network access quickly. 

### default.nix
This mainly handles python requirements for this project, similar to how a requirements.txt might in a docker project. 

### shell.nix
This sets the state of the shell. Particularly useful for local development with `nix develop`. 

### Statefulset.yaml
The main thing this handles as far as changes to this project go is environment variables and the containers it creates.
It creates three containers one main one called `data-pusher`, one for `celery-beat` to run the scheduler, and one for 
`celery-worker`'s. The main thing that changes here is env vars. 

## Current and future state of this project
It handles a handful of daily and monthly reports across email, external sftp, and our internal sftp. An example of new 
functionality it's been adding is for `CMBI London Close Report Email` we send them email + SFTP reports concurrently, which we were unable to do 
previously. It's intended to the same soon for Fidelity reports. Other recent additions like slack notifications unified 
across reports add more redundancy that we previously had, and with a bit of work we can add more features that will 
be useful like generating historical reports for all these cases and adding a more clear dashboard. A lot of our important 
clients and prospects want this functionality but the problem was the old system of running docker applications on the 
sftp server and similar things added a lot of development overhead and reliability issues.

## Proposed release process
This document was written with the engineering reorg in order to help inform a new release proces. There is some changes
that need to be made that are not specific to any product area, like [adding slack notifications](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/19)
or proper status page/ dashboard. These changes will need to be reviewed by someone on the platform team for release. 
Other changes to this project, for example adding a [new report for a customer](https://gitlab.com/coinmetrics/data-delivery/export/data-pusher/-/merge_requests/18) 
fall under a specific product area like indexes or metrics or something else, so that can be approved by those teams. 