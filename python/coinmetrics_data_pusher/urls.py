from django.urls import path, include

from coinmetrics_data_pusher.customer_reporting import views
from coinmetrics_data_pusher.customer_reporting.admin import admin_site

# admin.site.login = views.admin_login

urlpatterns = [
    path('oauth/start/', views.start_auth, name='start_auth'),
    path('accounts/oauth/callback/', views.oauth_callback, name='oauth_callback'),
    path('accounts/google/login/callback/', views.oauth_callback, name='oauth_callback'),
    path('admin/oauth/callback', views.oauth_callback, name='oauth_callback'),
    path('admin/', admin_site.urls),
    path('', include('django_prometheus.urls')),
]
