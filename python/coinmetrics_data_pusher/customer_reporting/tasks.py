from __future__ import absolute_import, unicode_literals

import base64
import datetime
import os
import sys

import paramiko
import pysftp
import pytz
from paramiko import SFTPAttributes, RSA<PERSON>ey, Ed25519Key
from paramiko.pkey import PKey

from .coinmetrics_api_scripts.util.coin_metrics_client_factory import CoinMetricsClientFactory
from .coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from .coinmetrics_api_scripts.util.retry_util import RetryUtil

sys.path.append("../")
from minio import Minio
from io import StringIO
from .models import CustomerReport
from django.core.exceptions import ObjectDoesNotExist
from .report_generators import ReportGenerator, ReportResults
from typing import Type
from celery import shared_task
from pprint import pprint
from coinmetrics_data_pusher.customer_reporting.common import REPORT_NAME_TO_GENERATOR_MAP, FILE_BACKUP_LOCATION
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Attachment, FileName, FileType, FileContent, Email

log = LoggerFactory.get_logger("Tasks")


@shared_task
def generate_and_send_report(customer_report_id: int, **kwargs) -> None:
    """
    This function will generate and send reports to customers. It is called by the PeriodicTask model in order to actually
    generate and send reports
    :param customer_report_id: int should correspond to the CustomerReport module
    :return: None, if successful will generate and send reports to customers
    """
    log.info(f"report id: {customer_report_id}, starting a task")
    try:
        customer_report: CustomerReport = CustomerReport.objects.get(pk=customer_report_id)
    except ObjectDoesNotExist as e:
        log.error(f"report id: {customer_report_id}, couldn't find customer report of ID: {customer_report_id}")
        raise e
    if datetime.datetime.now(tz=pytz.UTC) < customer_report.periodic_task.start_time and not "MANUAL_TRIGGER" in kwargs:
        log.info(f"report id: {customer_report_id}, exiting this task because it is running before the designated 'start_time' for the task, this task still "
                    f"may be manually triggered from the UI dashboard")
        return
    report_generator_class: Type[ReportGenerator] = REPORT_NAME_TO_GENERATOR_MAP[customer_report.report_name]
    log.info(f"report id: {customer_report_id}, creating and executing report generator: {report_generator_class}")
    report_generator = report_generator_class(customer_report=customer_report, folder_location=f"{FILE_BACKUP_LOCATION}/{customer_report_id}")
    if not report_generator.run_report():
        log.info(f"report id: {customer_report_id}, skipping report generator: {report_generator_class}")
        return None
    report_results = report_generator.generate_full_report(**kwargs)
    log.info(f"report id: {customer_report_id}, executed report generator: {report_generator_class}, result_files: {report_results.result_files} to be sent via: {report_results.customer_report.delivery_method}")
    try:
        if customer_report.delivery_method == "email":
            send_email_report_to_customer(report_results, customer_report)
        elif customer_report.delivery_method == "sftp":
            send_sftp_report_to_customer(report_results, customer_report)
        elif customer_report.delivery_method == "internal_sftp":
            send_internal_sftp_to_server(report_results, customer_report)
        elif customer_report.delivery_method == "sftp_private_key_auth":
            send_sftp_using_ed25519_privatekey(report_results, customer_report)
        elif customer_report.delivery_method == "sftp_rsa_private_key_auth":
            send_sftp_using_rsa_privatekey(report_results, customer_report)
        elif customer_report.delivery_method == "minio_file_upload":
            send_reports_to_minio(report_results, customer_report)
        elif customer_report.delivery_method == "monitoring_task":
            execute_monitoring_task(report_results, customer_report)
    except Exception as e:
        report_generator.handle_error(e, error_stage=f"ERROR DURING FILE DELIVERY: {customer_report.delivery_method}", **kwargs)
    log.info(f"report id: {customer_report_id}, task is completed for: {report_generator_class}")


def send_email_report_to_customer(report_results: ReportResults, customer_report: CustomerReport) -> None:
    """
    This function sends email reports based on report results. For now this only supports sending CSV/ Textfiles
    :param report_results: results from a CustomerReport to send out
    :return: None
    """
    report_id = customer_report.pk
    log.info(f"report id: {report_id}, preparing email for {report_results.customer_report}")
    to_emails: list[str] = report_results.customer_report.customer.email.split(",")
    from_email = Email(email=os.environ['SENDGRID_EMAIL'], name="CM Reporting Bot")
    api_key = os.environ['SENDGRID_API_KEY']
    client = SendGridAPIClient(api_key)
    email_body = report_results.email_body if report_results.email_body else f"<strong>{report_results.customer_report.friendly_report_description}</strong>"
    mail = Mail(
        from_email=from_email,
        to_emails=to_emails,
        subject=report_results.friendly_report_name,
        html_content=email_body
    )
    if report_results.result_files:
        for file_name in report_results.result_files:
            with open(file_name, 'rb') as file:
                data = file.read()
            encoded_file = base64.b64encode(data).decode()
            attachment = Attachment(
                FileContent(encoded_file), file_name=FileName(file_name.split("/")[-1]), file_type=FileType("text/csv")
            )
            mail.add_attachment(attachment)
    log.info(f"report id: {report_id}, sending email to {to_emails} for report: {report_results.customer_report}\n"
             f"file sending: {_join_result_files(report_results.result_files)}")
    client.send(mail)
    log.info(f"report id: {report_id}, sent email to {to_emails} for report: {report_results.customer_report}")
    cr_workload = report_results.customer_report_workload
    cr_workload.files_delivered_successfully = len(report_results.result_files) if report_results.result_files else 0
    cr_workload.status = "FILES DELIVERED"
    cr_workload.save()


def _join_result_files(result_files):
    if result_files is None:
        return []
    else:
        return ','.join(result_files)


def send_sftp_report_to_customer(report_results: ReportResults, customer_report: CustomerReport) -> None:
    """
    Sends SFTP files to customers based on the customer reports and the customer credentials
    :param report_results: ReportResults that come from running a ReportGenerator object. Contains a list of file
    locations that will be sent to the customer
    :param customer_report: CustomerReport object that contains the information on what task was run and who should be
    receiving it
    :return: None, will send files to customer
    """
    report_id = customer_report.pk
    _validate_sftp_path(report_id=report_id, sftp_path=customer_report.customer.sftp_path)
    cnopts = pysftp.CnOpts(knownhosts="~/.ssh/known_hosts")
    cnopts.hostkeys = None
    sftp_creds_and_options = customer_report.customer.get_sftp_creds_and_options()

    host = ""
    if "host" in sftp_creds_and_options:
        host = sftp_creds_and_options["host"]

    port = ""
    if "port" in sftp_creds_and_options:
        port = sftp_creds_and_options["port"]

    username = ""
    if "username" in sftp_creds_and_options:
        username = sftp_creds_and_options["username"]

    log_params = f"{username}@{host}:{port}"

    def _internal_get_sftp():
        log.info(f"report id: {report_id}, {log_params} Connecting")
        return pysftp.Connection(cnopts=cnopts, **sftp_creds_and_options)

    with RetryUtil.retry_function(func=_internal_get_sftp, max_retries=5, retry_delay=3) as sftp:
        log.info(f"report id: {report_id}, {log_params} Connection successfully established")
        files_sent_count = 0
        for file in report_results.result_files:
            file_name = file.split("/")[-1]
            full_sftp_path = os.path.join(customer_report.customer.sftp_path, file_name)
            log.info(f"report id: {report_id}, {log_params} Sending file {file} to SFTP Location: {full_sftp_path}")
            sftp.put(file, full_sftp_path, confirm=False)
            log.info(f"report id: {report_id}, {log_params} Sent file {file} to SFTP Location: {full_sftp_path}")
            files_sent_count += 1
    cr_workload = report_results.customer_report_workload
    cr_workload.files_delivered_successfully = files_sent_count
    cr_workload.status = "FILES DELIVERED"
    cr_workload.save()


def send_sftp_using_rsa_privatekey(report_results: ReportResults, customer_report: CustomerReport) -> None:
    report_id = customer_report.pk
    log.info(f"report id: {report_id}, using RSA private key for connection")
    pk: RSAKey = paramiko.RSAKey.from_private_key(StringIO(_get_decoded_private_key('CM_SFTP_RSA_PRIVATE_KEY')))
    send_sftp_using_privatekey(report_results=report_results, customer_report=customer_report, pk=pk)


def send_sftp_using_ed25519_privatekey(report_results: ReportResults, customer_report: CustomerReport) -> None:
    report_id = customer_report.pk
    log.info(f"report id: {report_id}, using Ed25519 private key for connection")
    pk: Ed25519Key = paramiko.Ed25519Key.from_private_key(StringIO(_get_decoded_private_key('CM_SFTP_PRIVATE_KEY')))
    send_sftp_using_privatekey(report_results=report_results, customer_report=customer_report, pk=pk)


def _get_decoded_private_key(env_variable: str) -> str:
    pk_encoded = os.environ[env_variable]
    return base64.b64decode(pk_encoded).decode("utf-8")


def send_sftp_using_privatekey(report_results: ReportResults, customer_report: CustomerReport, pk: PKey) -> None:
    """
    Sends files over SFTP using private key auth
    :param report_results: ReportResults object that comes from a ReportGenerator class, contains a list of the result
    files to be sent to the server
    """
    report_id = customer_report.pk
    _validate_sftp_path(report_id=report_id, sftp_path=customer_report.customer.sftp_path)
    server = customer_report.customer.sftp_hostname
    username = customer_report.customer.sftp_username
    port = customer_report.customer.sftp_port

    def _internal_send_sftp_using_privatekey() -> int:
        ssh = None
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            pk_name = pk.__class__.__name__
            log.info(f"report id: {report_id}, connecting to SFTP server using {pk_name}: {username}@{server}:{port}")
            ssh.connect(server, username=username, port=port, pkey=pk)
            files_delivered_count = 0
            with ssh.open_sftp() as sftp:
                for file in report_results.result_files:
                    just_file_name = file.split("/")[-1]
                    full_remote_path = os.path.join(customer_report.customer.sftp_path, just_file_name)
                    log.info(f"report id: {report_id}, sending file: {just_file_name} to SFTP Server: {full_remote_path}")
                    try:
                        sftp.put(file, full_remote_path, confirm=False)
                    except OSError as e:
                        if 'size mismatch in put' in str(e):
                            """
                            One potential reason for this discrepancy could be the file transfer mode.
                            Paramiko's put method might not be waiting for the file to be completely written to the server,
                            which can cause the size mismatch error even though the file has been transferred successfully.
                            To resolve this, we attempt to retrieve file statistics in a loop, making several attempts until we obtain a non-zero file size.
                            """

                            def _internal_get_stat():
                                stat: SFTPAttributes = sftp.stat(full_remote_path)
                                log.warning(f"Size mismatch error occurred when dropping file {file} to {full_remote_path} for customer report: "
                                               f"{customer_report.report_name} id: {report_id}, the file stat: {stat}"
                                               f"If the file size is 0 IOError will be raised.")
                                if stat.st_size == 0:
                                    raise e

                            RetryUtil.retry_function(func=_internal_get_stat, max_retries=5, retry_delay=2)
                        else:
                            raise e

                    log.info(f"report id: {report_id}, sent file: {just_file_name} to SFTP Server: {full_remote_path}")
                    files_delivered_count += 1
            return files_delivered_count
        finally:
            if ssh:
                ssh.close()

    cr_workload = report_results.customer_report_workload
    cr_workload.files_delivered_successfully = RetryUtil.retry_function(func=_internal_send_sftp_using_privatekey,
                                                                        max_retries=3, retry_delay=3)
    cr_workload.status = "FILES DELIVERED"
    cr_workload.save()


def send_internal_sftp_to_server(report_results: ReportResults, customer_report: CustomerReport) -> None:
    """
    Sends files to CoinMetrics internal SFTP Server
    :param report_results: ReportResults object that comes from a ReportGenerator class, contains a list of the result
    files to be sent to the server
    """
    report_id = customer_report.pk
    log.info(f"report id: {report_id}, trying to send files over internal SFTP: {report_results.result_files}")
    _validate_sftp_path(report_id=report_id, sftp_path=customer_report.customer.sftp_path)
    server = os.environ.get("CM_SFTP_SERVER", "sftp.coinmetrics.io")
    username = os.environ.get("CM_SFTP_USERNAME", "data-pusher.flat.files")
    port = os.environ.get("CM_SFTP_PORT", 2222)
    private_key = StringIO(base64.b64decode(os.environ['CM_SFTP_PRIVATE_KEY']).decode("utf-8"))
    pk = paramiko.Ed25519Key.from_private_key(private_key)

    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        log.info(f"report id: {report_id}, connecting to CM SFTP server: {username}@{server}:{port}")
        ssh.connect(server, username=username, port=port, pkey=pk)
        files_delivered_count = 0
        with ssh.open_sftp() as sftp:
            for file in report_results.result_files:
                just_file_name = file.split("/")[-1]
                full_remote_path = os.path.join(customer_report.customer.sftp_path, just_file_name)
                log.info(f"report id: {report_id}, sending file: {just_file_name} to CM SFTP Server: {full_remote_path}")
                try:
                    sftp.put(localpath=file, remotepath=full_remote_path, confirm=False)
                except IOError as e:
                    log.info(msg=f"report id: {report_id}, tried, but failed to send the file: {file} to {full_remote_path}, raising error")
                    raise e
                log.info(f"report id: {report_id}, sent file: {just_file_name} to CM SFTP Server: {full_remote_path}")
                files_delivered_count += 1
    finally:
        if ssh:
            ssh.close()

    cr_workload = report_results.customer_report_workload
    cr_workload.files_delivered_successfully = files_delivered_count
    cr_workload.status = "FILES DELIVERED"
    cr_workload.save()


def _validate_sftp_path(report_id: str, sftp_path: str):
    if not sftp_path or not sftp_path.strip():
        message = f"report id: {report_id}, SFTP path '{sftp_path}' is empty."
        log.error(message)
        raise ValueError(message)


def send_reports_to_minio(report_results: ReportResults, customer_report: CustomerReport):
    """
    This task is used to send files to our minio bucket
    """
    report_id = customer_report.pk
    endpoint = os.environ.get("MINIO_URL", "minio.cnmtrcs.io:9002")
    if endpoint.startswith("https://"):
        endpoint = endpoint.split("//")[1]
    minio_access_key = os.environ.get("MINIO_ACCESS_KEY_ID")
    minio_secret_key = os.environ.get("MINIO_SECRET_ACCESS_KEY")
    if not all([minio_secret_key, minio_access_key]):
        raise AssertionError("MINIO_ACCESS_KEY_ID and MINIO_SECRET_ACCESS_KEY must set in env in "
                             "order to drop files to minio")
    minio_region = os.environ.get("MINIO_REGION", "eu-hel1-hetz")
    minio_bucket = customer_report.customer.minio_bucket
    if not minio_bucket:
        log.info(f"report id: {report_id}, exiting worker for customer report because for {customer_report.customer} no minio_bucket is set")
        raise

    client = Minio(
        endpoint=endpoint,
        access_key=minio_access_key,
        secret_key=minio_secret_key,
        region=minio_region
    )
    for file in report_results.result_files:
        just_file_name = file.split("/")[-1]
        client.fput_object(bucket_name=customer_report.customer.minio_bucket,
                           file_path=file,
                           object_name=just_file_name)
    cr_workload = report_results.customer_report_workload
    cr_workload.status = "FILES DELIVERED"
    cr_workload.save()


def execute_monitoring_task(report_results: ReportResults, customer_report: CustomerReport) -> None:
    """
    This function can be used for monitoring.
    :param report_results: results from a CustomerReport to send out
    :param customer_report: CustomerReport object that contains the information on what task was run and who should be receiving it
    :return: None
    """
    report_id = customer_report.pk
    log.debug(f"report id: {report_id}, monitoring task for {report_results.customer_report}")


@shared_task
def log_catalog_metrics() -> None:
    """
    Method used for debugging celery
    """
    client = CoinMetricsClientFactory.get_client()
    catalog_metrics = client.catalog_metrics()
    pprint(catalog_metrics, indent=4)


@shared_task
def auth_cm_sftp_test() -> None:
    """
    Method to test connection to CM SFTP server based on credentials
    """
    username = os.environ["CM_SFTP_USERNAME"]
    server = os.environ["CM_SFTP_SERVER"]
    port = int(os.environ["CM_SFTP_PORT"])
    pkey = StringIO(base64.b64decode(os.environ["CM_SFTP_PRIVATE_KEY"]).decode())
    pkey = paramiko.Ed25519Key.from_private_key(pkey)

    ssh = None
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy)
        ssh.connect(hostname=server, port=port, pkey=pkey, username=username)
        sftp = ssh.open_sftp()
        log.info(msg=f"Printing sftp listdir as confirmation: {sftp.listdir()}")
        print(sftp.listdir())
    finally:
        if ssh:
            ssh.close()
