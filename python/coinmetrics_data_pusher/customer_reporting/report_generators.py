import datetime
import json
import os
import re
import zipfile
from dataclasses import dataclass, field
from html import unescape
from typing import List

import pytz
import requests
from django.core.management import call_command

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.cmbi_export.cmbi_indexes_export import \
    generate_cmbi_eth_close, \
    generate_cmbi_eth_file_rt, generate_cmbi_btc_file_rt, generate_cmbi_btc_close_file
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.cmbi_export.grayscale_export import \
    create_grayscale_report
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.dda.cmbi_london_close_report import \
    create_dda_cmbi_london_close_file
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.export_atlas_data import \
    generate_monthly_reference_rates_file
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_close_price_exporter import \
    FidelityNYClosePriceExporter, FidelityLondonClosePriceExporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_constituents_report import \
    generate_fidelity_constituents_file
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_date_resolver import \
    FidelityDateResolver
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_index_exporters import \
    SingleAssetFidelityPair, MultiAssetFidelityPair, FidelityEvenIndexPair
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.last_day_of_month_rr_file import \
    generate_end_of_month_daily_rr_file
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.globalx_report import \
    GlobalXReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.london_close import \
    CMBILondonCloseReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.ny_close import \
    CMBINYCloseReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.osprey import \
    OspreyEmailReport
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.singapore_close import \
    CMBISingaporeCloseReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.check_close import \
    IndexReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.fidelity_ny_close_compare import \
    FidelityNYCloseCompareReporter
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.oauth_utils import \
    upload_to_google_drive
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monthly_metrics_file import MonthlyMetricsFile
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.env_util import is_local, is_staging
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.holiday_util import is_early_close_day
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.models import CustomerReportWorkload, CustomerReport
from coinmetrics_data_pusher.customer_reporting.utility_scripts.database_backup import create_db_backup

log = LoggerFactory.get_logger("ReportGenerators")


@dataclass
class ReportResults:
    """
    Contains results from running a report. Meant to hold data/ objects from running reports
    """
    customer_report: CustomerReport
    customer_report_workload: CustomerReportWorkload
    report_status: str
    friendly_report_name: str
    email_body: str = field(default_factory=str)
    result_files: List[str] = field(default_factory=list)
    files_to_backup: List[str] = field(default_factory=list)


class ReportGenerator:
    """
    This is an abstract class to generate reports for customers. Adds common interface/ logging for creating reports
    """
    SEND_PROD_TO_ALERTS_MD = False
    SEND_NON_PROD_TO_ALERTS_MD = False
    SLACK_WEBHOOK_URL_STG = "*******************************************************************************"
    ALERTS_MD_SLACK_WEBHOOK = "*******************************************************************************"
    SLACK_WEBHOOK_URL_PROD = "*******************************************************************************"
    BACKUP_REPORTS = True  # Reports will be backed up to google drive by default on staging and prod
    GOOGLE_DRIVE_FOLDER_ID_LOCAL = "1CoCF7Lc8HTmh_HqzOg6CkQ4LVxC624Nw"
    GOOGLE_DRIVE_FOLDER_ID_STG = "19IlC1XWZv_XmN34rxdNK4H13-BNtxHmX"
    GOOGLE_DRIVE_FOLDER_ID_PROD = "18eF6F-o-swrOxeEWVE5FDfSa75eOi47M"  # If we want to backup files to a specific folder for prod, change this to

    def __init__(self, customer_report: CustomerReport, folder_location: str):
        self.customer_report = customer_report
        self.folder_location = folder_location
        self.report_workload: CustomerReportWorkload = None
        self.report_start_time: datetime.datetime = None
        self.timely_report_name = self.generate_date_based_report_name()
        if not os.path.exists(folder_location):
            os.makedirs(folder_location)

    def run_report(self) -> bool:
        """
        This is to add custom logic to running a report. If it returns false it will not run the report and
        instead the thread will exit. True by default. A case where this might be useful is adding some logic like
        "only run report every third business day of the month" where we need custom logic
        """
        return True

    def generate_full_report(self, **kwargs) -> ReportResults:
        self.start_generating_report()
        try:
            report_results = self.generate_report()
        except Exception as e:
            self.handle_error(e, error_stage="ERROR DURING REPORT GENERATION", **kwargs)
        try:
            self.backup_reports(report_results=report_results)
        except Exception as e:
            self.handle_error(e, error_stage="ERROR DURING REPORT BACKUP")
        report_results = self.finish_report(report_results)
        if (self.SEND_PROD_TO_ALERTS_MD and os.environ.get("ENVIRONMENT") == "PRODUCTION" or
                self.SEND_NON_PROD_TO_ALERTS_MD and os.environ.get("ENVIRONMENT") != "PRODUCTION"):
            self.send_report_to_alerts_md(report_results=report_results)
        return report_results

    def start_generating_report(self) -> None:
        """
        This creates an entry for CustomerReportWorkloads so that there is an audit trail of what jobs are triggered
        """
        self.report_start_time = datetime.datetime.now(pytz.UTC)
        report_workload = CustomerReportWorkload(customer_report=self.customer_report, status="IN PROGRESS", start_time_task=self.report_start_time)
        report_workload.save()
        self.report_workload = report_workload

    def generate_report(self) -> ReportResults:
        """
        This method will contain the business logic to run reports, it must be implemented on a per report basis,
        otherwise will raise non implemented error
        :return: returns a tuple containg the list of files in the
        """
        raise NotImplementedError

    def finish_report(self, report_results: ReportResults) -> ReportResults:
        """
        Run at the end of the report to update that the report was a success or failure
        :param report_results: updates report_workload.status with report_results.report_status
        :return: ReportResults
        """
        report_id = self.customer_report.pk
        if self.report_workload is not None:
            self.report_workload.status = report_results.report_status
            time = datetime.datetime.now(tz=pytz.UTC)
            self.report_workload.end_time_task = time
            self.report_workload.save()

        if self.customer_report.zip_report_files:
            new_files = []
            log.info(f"report id: {report_id}, zipping files: {report_results.result_files}")
            for file in report_results.result_files:
                just_file_name = file.split("/")[-1]
                zip_file_name = file + ".zip"
                log.info(f"report id: {report_id}, zipping with filename: {just_file_name}")
                with zipfile.ZipFile(zip_file_name, "a") as zip_file:
                    zip_file.write(file, arcname=just_file_name)
                new_files.append(zip_file_name)
                os.remove(file)
            report_results.result_files = new_files

        return report_results

    def generate_date_based_report_name(self) -> str:
        """
        Generates a friendly report name based on the time. By default will just just add the month and day to a report name
        i.e. Monthly Reference Rate Report + 2022-09-01
        :return: the CustomerReport.report_name attribute with the date appended
        """
        report_name = self.customer_report.report_name
        date_string = datetime.date.today()
        return report_name + " " + str(date_string)

    def handle_error(self, err: Exception, error_stage: str, raise_err: bool = True, **kwargs) -> None:
        """
        This function unifies error handling. For staging environment will send a slack message to the
        staging alerts channel #guild-data-delivery-data-push-alerts-stg and #guild-data-delivery-data-push-prod
        will be used for prod.
        :param err: Exception to raise after error handling
        :param error_stage: stage the error happened in. This will be used in the error message to slack as well as
        recorded in the report_workload db item.
        """
        report_id = self.customer_report.pk
        if 'MANUAL_TRIGGER' in kwargs and 'ENVIRONMENT' in os.environ and os.environ['ENVIRONMENT'] == "PRODUCTION":
            log.error(f"report id: {report_id}, skipping sending error message "
                     "because this was a manual trigger on production. "
                     "This is to avoid excessive messaging in the alerts channel.", exc_info=err)
            raise err

        if self.report_workload:
            self.report_workload.status = error_stage
            self.report_workload.failure_message = f"Error during {error_stage}: {err}"
            self.report_workload.save()

        if 'ENVIRONMENT' not in os.environ:
            log.error(f"report id: {report_id}, no environment specified, raising error without slack notification", exc_info=err)
            raise err

        if os.environ['ENVIRONMENT'] == "LOCAL":
            log.error(msg=f"report id: {report_id}, logging error but not raising so that report can still be sent. Customer report name: {self.customer_report.report_name} Error during {error_stage}. Error:\n{err}")
            raise err
        elif os.environ['ENVIRONMENT'] == "STAGING":
            slack_webhook_url = self.SLACK_WEBHOOK_URL_STG
        elif os.environ['ENVIRONMENT'] == "PRODUCTION":
            slack_webhook_url = self.SLACK_WEBHOOK_URL_PROD
        else:
            raise err
        slack_message = f"There was a failure in the report: {report_id} {self.customer_report.report_name}. \n" \
                        f"This error occurred during stage: {error_stage} \n" \
                        f"Error: {err} \n" \
                        f"Please acknowledge that this error is being looked by responding with the eye emoji"
        requests.post(slack_webhook_url, data=json.dumps({"text": slack_message}),
                      headers={'Content-Type': 'application/json'})

        if not raise_err:
            log.error(msg=f"report id: {report_id}, logging error but not raising so that report can still be sent. Error during {error_stage}. Error:\n{err}")
            return

        raise err

    def backup_reports(self, report_results: ReportResults) -> None:
        """
        Optional step to upload files to google drive. By default will not doing anything, but can be
        configured to backup specific reports to google drive either to a generic data pusher folder or a specific folder,
        for example some of the fidelity reports already get uploaded to google drive.
        """
        if not self.BACKUP_REPORTS:
            return
        environment = os.environ.get("ENVIRONMENT")
        _get_google_drive_folder_for_env = {
            "PRODUCTION": self.GOOGLE_DRIVE_FOLDER_ID_PROD,
            "STAGING": self.GOOGLE_DRIVE_FOLDER_ID_STG,
            "LOCAL": self.GOOGLE_DRIVE_FOLDER_ID_LOCAL
        }
        google_drive_folder_id = _get_google_drive_folder_for_env[environment]
        for file_path in report_results.files_to_backup:
            with open(file_path, 'r') as file:
                file_data = file.read()
            file_name = file_path.split("/")[-1]
            upload_to_google_drive(folder_id=google_drive_folder_id, name=file_name, data=file_data)

    def send_report_to_alerts_md(self, report_results: ReportResults) -> None:
        """
        If the flag SEND_PROD_TO_ALERTS_MD is set and it's on prod then reports will be sent to alerts md. This is
        mainly relevant for email based reports, where we want to see the text in email. It will only send what the
        email body would've been for now.
        """
        report_text = self.html_to_paragraph(report_results.email_body)
        requests.post(self.ALERTS_MD_SLACK_WEBHOOK, data=json.dumps({"text": report_text}),
                      headers={'Content-Type': 'application/json'})

    @staticmethod
    def paragraph_to_html(paragraph: str) -> str:
        """
        Convert a paragraph stored in a string object to basic HTML with <br> breaks and proper spacing.

        Args:
            paragraph (str): The paragraph text as a string.

        Returns:
            str: The converted paragraph as an HTML string with <br> tags for line breaks.
        """

        # Replace newline characters with <br> tags
        html_paragraph = paragraph.replace('\n', '<br>')

        # Wrap the resulting HTML in a <p> tag for proper spacing
        html_paragraph = f'<p>{html_paragraph}</p>'

        return html_paragraph

    @staticmethod
    def html_to_paragraph(html_paragraph: str) -> str:
        """
        Convert an HTML string back to a plain text string.

        Args:
            html_paragraph (str): The HTML string.

        Returns:
            str: The converted string as plain text.
        """

        # Unescape any HTML entities (like &amp;, &lt;, etc.)
        text_paragraph = unescape(html_paragraph)

        # Remove <p> and </p> tags
        text_paragraph = text_paragraph.replace('<p>', '').replace('</p>', '')

        # Replace <br> or <br/> tags with newline characters
        text_paragraph = re.sub('<br\s*?/?>', '\n', text_paragraph)

        return text_paragraph

    @staticmethod
    def get_report_date(hour: int, timezone: str) -> datetime.date:
        ny_tz = pytz.timezone(timezone)
        ny_time = datetime.datetime.now(ny_tz)
        report_date = datetime.date.today() if ny_time.hour >= hour and ny_time.date() == datetime.date.today() else datetime.date.today() - datetime.timedelta(days=1)
        return report_date

    @staticmethod
    def get_report_date_est(hour: int) -> datetime.date:
        """
        This function is for use to get the report date. At the time it is run, if it is after a particular eastern time it will return
        the current day, otherwise it will return the previous day. It's intended use is that many of our reports are meant
        to be sent out, or the data queried for a particular eastern time, such as sending closing prices to many clients. So if this is
        run before a particular eastern time, it will send the report for the previous day since the next closing prices are available yet
        """
        return ReportGenerator.get_report_date(hour=hour, timezone="America/New_York")

    @staticmethod
    def get_report_date_4pm_est() -> datetime.date:
        """
        This function is for use to get the report date. At the time it is run, if it is after 4pm eastern it will return
        the current day, otherwise it will return the previous day. It's intended use is that many of our reports are meant
        to be sent out, or the data queried for 4pm eastern, such as sending closing prices to many clients. So if this is
        run at 2pm, it will send the report for the previous day since the next closing prices are available yet
        """
        return ReportGenerator.get_report_date_est(16)

    @staticmethod
    def get_report_date_1pm_est() -> datetime.date:
        """
        This function is for use to get the report date. At the time it is run, if it is after 1pm eastern it will return
        the current day, otherwise it will return the previous day. It's intended use is that many of our reports are meant
        to be sent out, or the data queried for 1pm eastern, such as sending closing prices to many clients. So if this is
        run at 11am, it will send the report for the previous day since the next closing prices are available yet
        """
        return ReportGenerator.get_report_date_est(13)

    @staticmethod
    def get_report_date_london(hour: int) -> datetime.date:
        return ReportGenerator.get_report_date(hour=hour, timezone="Europe/London")

    @staticmethod
    def get_report_date_4pm_london() -> datetime.date:
        return ReportGenerator.get_report_date_london(hour=16)


class AtlasFundReport(ReportGenerator):
    """
    Generates report for Atlas fund. The report is a monthly reference rates summary, just a single file.
    """

    def generate_report(self) -> ReportResults:
        result_file_name = generate_monthly_reference_rates_file(folder_location=self.folder_location)
        return ReportResults(result_files=[result_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class CastleIslandEomReport(ReportGenerator):
    """
    Generates the CIV EOM RR report
    """

    def generate_report(self) -> ReportResults:
        result_file_name = generate_end_of_month_daily_rr_file(folder_location=self.folder_location)
        return ReportResults(result_files=[result_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class AbstractFidelityReportGenerator(ReportGenerator):
    """
    This class just exists currently to override the GOOGLE_DRIVE_FOLDER_ID parameters so that fidelity reports go to
    a particular place. We may decided to add additional functionality to ensure quality/ reliability/ observability
    of Fidelity reports
    """
    BACKUP_REPORTS = True
    GOOGLE_DRIVE_FOLDER_ID_PROD = "1jx7IEy3wcpS__y5NNpCPynrxviL9HdVc"


class FidelityDailyBTCIndexReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidbtc"
        report_date = self.get_report_date_4pm_est()
        fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
        index_report_file_name = fidbtc_report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyBTCIndexLondonReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidbtcl"
        report_date = self.get_report_date_4pm_london()
        fidbtcl_report_generator = SingleAssetFidelityPair.create_fidbtcl_reporter(reporting_date=report_date)
        index_report_file_name = fidbtcl_report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyETHIndexReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fideth"
        report_date = self.get_report_date_4pm_est()
        fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date)
        index_report_file_name = fideth_report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyBTCRRIndexReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidbcr"
        report_date = self.get_report_date_4pm_est()
        report_generator = SingleAssetFidelityPair.create_fidbcr_reporter(reporting_date=report_date)
        index_report_file_name = report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyETHRRIndexReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fiderr"
        report_date = self.get_report_date_4pm_est()
        report_generator = SingleAssetFidelityPair.create_fiderr_reporter(reporting_date=report_date)
        index_report_file_name = report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailySOLRRIndexReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidsol"
        report_date = self.get_report_date_4pm_est()
        report_generator = SingleAssetFidelityPair.create_fidsol_reporter(reporting_date=report_date)
        index_report_file_name = report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyBTCETHEvenReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidebe"
        report_date = self.get_report_date_4pm_est()
        fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date)
        index_report_file_name = fidbe_report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityDailyBTCETHCapWeightedReport(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        file_prefix = "fidbei"
        report_date = self.get_report_date_4pm_est()
        fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
        index_report_file_name = fidbei_report_generator.generate_daily_index_report(self.folder_location, file_prefix)
        return ReportResults(result_files=[index_report_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class Fidelity3PMRealTimeCombinedIndexReports(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        hour_end_est_3pm = 15
        fidbtc_file_prefix = "fidbtc"
        report_date = self.get_report_date_est(hour_end_est_3pm)
        fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date,
                                                                                 hour=hour_end_est_3pm)
        fidbtc_index_report_file_name = fidbtc_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fidbtc_file_prefix)

        fideth_file_prefix = "fideth"
        fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date,
                                                                                 hour=hour_end_est_3pm)
        fideth_index_report_file_name = fideth_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fideth_file_prefix)

        fidbe_file_prefix = "fidebe"
        fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date,
                                                                              hour=hour_end_est_3pm)
        fidbe_index_report_file_name = fidbe_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                             fidbe_file_prefix)

        fidbei_file_prefix = "fidbei"
        fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date,
                                                                                hour=hour_end_est_3pm)
        fidbei_index_report_file_name = fidbei_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fidbei_file_prefix)

        all_files = [fidbtc_index_report_file_name, fideth_index_report_file_name, fidbe_index_report_file_name,
                     fidbei_index_report_file_name]

        return ReportResults(result_files=all_files, customer_report=self.customer_report,
                             customer_report_workload=self.report_workload,
                             report_status="REPORT GENERATED", friendly_report_name=self.timely_report_name)


class Fidelity4PMRealTimeCombinedIndexReports(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()

        fidbtc_file_prefix = "fidbtc"
        fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
        fidbtc_index_report_file_name = fidbtc_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fidbtc_file_prefix)

        fideth_file_prefix = "fideth"
        fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date)
        fideth_index_report_file_name = fideth_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fideth_file_prefix)

        fidbe_file_prefix = "fidebe"
        fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date)
        fidbe_index_report_file_name = fidbe_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                             fidbe_file_prefix)

        fidbei_file_prefix = "fidbei"
        fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
        fidbei_index_report_file_name = fidbei_report_generator.generate_realtime_index_report(self.folder_location,
                                                                                               fidbei_file_prefix)

        all_files = [fidbtc_index_report_file_name, fideth_index_report_file_name, fidbe_index_report_file_name,
                     fidbei_index_report_file_name]

        return ReportResults(result_files=all_files, customer_report=self.customer_report,
                             customer_report_workload=self.report_workload,
                             report_status="REPORT GENERATED", friendly_report_name=self.timely_report_name)


class FidelityCombinedDailyIndexReports(AbstractFidelityReportGenerator):

    def generate_report(self) -> ReportResults:
        fidbtc_file_prefix = "fidbtc"
        report_date = self.get_report_date_4pm_est()
        fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
        fidbtc_index_report_file_name = fidbtc_report_generator.generate_daily_index_report(self.folder_location,
                                                                                            fidbtc_file_prefix)

        fideth_file_prefix = "fideth"
        fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date)
        fideth_index_report_file_name = fideth_report_generator.generate_daily_index_report(self.folder_location,
                                                                                            fideth_file_prefix)

        fidbe_file_prefix = "fidebe"
        fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date)
        fidbe_index_report_file_name = fidbe_report_generator.generate_daily_index_report(self.folder_location,
                                                                                          fidbe_file_prefix)

        fidbei_file_prefix = "fidbei"
        fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
        fidbei_index_report_file_name = fidbei_report_generator.generate_daily_index_report(self.folder_location,
                                                                                            fidbei_file_prefix)

        all_files = [fidbtc_index_report_file_name, fideth_index_report_file_name, fidbe_index_report_file_name,
                     fidbei_index_report_file_name]

        return ReportResults(result_files=all_files, customer_report=self.customer_report,
                             customer_report_workload=self.report_workload,
                             report_status="REPORT GENERATED", friendly_report_name=self.timely_report_name)


class FidelityConstituentsReport(AbstractFidelityReportGenerator):
    """
    This report generates a file in the format 'fidelity-constituents-YYYY-MM-DD.csv' that for each day gets data
    and does calculations for asset returns, marketcap, weight, indexshares and other information
    """

    def generate_report(self) -> ReportResults:
        timezone = pytz.timezone("America/New_York")
        report_date = datetime.date.today() if datetime.datetime.now(timezone).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
        file_name = generate_fidelity_constituents_file(folder_location=self.folder_location,
                                                        reporting_date=report_date)
        return ReportResults(result_files=[file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityProformaConstituentReport(AbstractFidelityReportGenerator):

    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)

    def run_report(self):
        """
        This method is overriden so that it only will run when the reporting date is within the proforma window. The
        proforma window is considered between the new data capture date and the date it takes effect. An example of a
        proforma window is between June 2nd 2023 and June 15th 2023, since June 1st a new data capture date and
        June 16th is the date that data capture dates takes effect.
        """
        report_id = self.customer_report.pk
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)

        run_report = FidelityDateResolver.is_in_proforma_reporting_window(report_date=report_date)

        if not run_report and (is_local() or is_staging()):
            log.info(f"report id: {report_id}, {self.__class__.__name__} run_report: result: {run_report}, "
                     f"but returning always True since we are in LOCAL or STAGING")
            return True

        return run_report

    def generate_report(self) -> ReportResults:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)

        proforma_date = FidelityDateResolver.resolve_capture_date_or_fail(report_date=report_date)
        file_name = generate_fidelity_constituents_file(folder_location=self.folder_location,
                                                        reporting_date=report_date,
                                                        proforma_date=proforma_date)
        return ReportResults(result_files=[file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class CMBIExportBTCFiles(ReportGenerator):
    """
    This class generates files for the BTC CMBI exports on the SFTP server. It generates two files in the format:
    `cmbi-btc-rt-YYYY-MM-DD.csv` and `cmbi-btc-close-YYYY-MM-DD.csv`. The first file contains prices at 15s interval for
    the past day, and the second has prices at the close for. Both files are for CMBIBTC index levels and spain from
    4pm-4pm daily
    """

    def generate_report(self) -> ReportResults:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
        realtime_file = generate_cmbi_btc_file_rt(report_date=report_date, output_directory=self.folder_location)
        close_file = generate_cmbi_btc_close_file(report_date=report_date, output_directory=self.folder_location)
        return ReportResults(result_files=[realtime_file, close_file], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class CMBIExportETHFiles(ReportGenerator):
    """
    This class generates files for the BTC CMBI exports on the SFTP server. It generates two files in the format:
    `cmbi-btc-rt-YYYY-MM-DD.csv` and `cmbi-btc-close-YYYY-MM-DD.csv`. The first file contains prices at 15s interval for
    the past day, and the second has prices at the close for. Both files are for CMBIBTC index levels and spain from
    4pm-4pm daily
    """

    def generate_report(self) -> ReportResults:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
        realtime_file = generate_cmbi_eth_file_rt(report_date=report_date, output_directory=self.folder_location)
        close_file = generate_cmbi_eth_close(report_date=report_date, output_directory=self.folder_location)
        return ReportResults(result_files=[realtime_file, close_file], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class GrayscaleAssetsReport(ReportGenerator):
    """
    This class generates files for a grayscale report that is emailed to them daily. It gets daily realtime reference
    rates for a list of assets at 4pm each day.
    """

    def generate_report(self) -> ReportResults:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
        grayscale_report = create_grayscale_report(report_date=report_date, folder_location=self.folder_location)
        return ReportResults(result_files=[grayscale_report], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)

    def generate_date_based_report_name(self) -> str:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("America/New_York")).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
        email_subject = f"Coin Metrics Crypto Rates New York {report_date.strftime('%m/%d/%Y')}"
        return email_subject


class DataBaseBackupReport(ReportGenerator):
    """
    This class is not client facing, it just generates database backups and uploads them to our minio bucket.
    """

    def run_report(self) -> bool:
        """
        Overwriting this method so that it can only be run and send to minio. This prevents this db file from being
        shared outside of the CM minio universe
        """
        return self.customer_report.delivery_method == "minio_file_upload"

    def generate_report(self) -> ReportResults:
        backup_file_path = create_db_backup(file_backup_location=self.folder_location)
        return ReportResults(result_files=[backup_file_path], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class AbstractFidelityFIDMonitorReport(AbstractFidelityReportGenerator):
    """
    This report is migrated from the monitor project. Note the the variable's BACKUP_REPORTS and
    GOOGLE_DRIVE_FOLDER_ID_PROD are overwritten so that this report will be both backed up and go to a specific place.
    """

    SEND_PROD_TO_ALERTS_MD = True

    hour: int

    def __init__(self,
                 index_suffix: str,
                 customer_report: CustomerReport,
                 folder_location: str,
                 hour=16,
                 timezone="America/New_York"):
        super().__init__(customer_report, folder_location)
        self.hour = hour
        self.timezone = timezone
        self.index_suffix = index_suffix

    def get_report_date(self, **kwargs) -> datetime:
        return ReportGenerator.get_report_date(hour=self.hour, timezone=self.timezone)

    def create_txtfile_backup(self, report_text: str, file_name: str) -> str:
        """
        Creates a txt file backup of the fidelity index report
        :param report_text: str data for the report
        :param file_name: name for the file
        :return: str full file location
        """
        full_folder_location = os.path.join(self.folder_location, file_name)
        with open(full_folder_location, 'w') as file:
            file.write(report_text)
        return full_folder_location

    def send_report_to_alerts_md(self, report_results: ReportResults) -> None:
        """
        If the flag SEND_PROD_TO_ALERTS_MD is set and it's on prod then reports will be sent to alerts md. This is
        mainly relevant for email based reports, where we want to see the text in email. It will only send what the
        email body would've been for now.
        """
        date = self.get_report_date()
        date_with_time = datetime.datetime(year=date.year, month=date.month, day=date.day, hour=self.hour)
        title_of_report = f"Fidelity {self.index_suffix} Index Report for {self.timezone}: {date_with_time.strftime('%Y-%m-%dT%H:%M:%S%z')}\n\n"
        report_text = title_of_report + self.html_to_paragraph(report_results.email_body)
        requests.post(self.ALERTS_MD_SLACK_WEBHOOK, data=json.dumps({"text": report_text}),
                      headers={'Content-Type': 'application/json'})

    def run_report_for_early_close(self) -> bool:
        report_id = self.customer_report.pk
        report_date = self.get_report_date()
        run_report = is_early_close_day(report_date)
        if is_local() or is_staging():
            log.info(f"report id: {report_id}, {self.__class__.__name__} run_report: result: {run_report}, "
                     f"but returning always True since we are in LOCAL or STAGING")
            return True
        return run_report


class AbstractFidelityFIDBTCMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("BTC", customer_report, folder_location, hour)

    def generate_report(self) -> ReportResults:
        """
        There's no files to send in this report so it will not attach any files
        """
        report_date = self.get_report_date()
        fidbtc_reporter = IndexReporter(index="FIDBTC", report_date=report_date, hour=self.hour)
        close_report = fidbtc_reporter.create_close_report()
        file_name = f"FIDBTC-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDBTC Index Report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class FidelityFIDBTCMonitorReport(AbstractFidelityFIDBTCMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)


class FidelityFIDBTCMonitorReport3pm(AbstractFidelityFIDBTCMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 15)


class FidelityFIDBTCMonitorEarlyCloseReport(AbstractFidelityFIDBTCMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 13)

    def run_report(self) -> bool:
        return self.run_report_for_early_close()


class FidelityFIDBTCLMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("BTCL", customer_report, folder_location, hour, timezone="Europe/London")

    def generate_report(self) -> ReportResults:
        """
        There's no files to send in this report so it will not attach any files
        """
        report_date = self.get_report_date()
        fidbtcl_reporter = IndexReporter(index="FIDBTC",
                                         index_email_name="FIDBTCL",
                                         report_date=report_date,
                                         hour=self.hour,
                                         timezone="Europe/London")
        close_report = fidbtcl_reporter.create_close_report()
        file_name = f"FIDBTCL-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDBTCL Index Report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class AbstractFidelityFIDETHMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("ETH", customer_report, folder_location, hour)

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date()
        fidbtc_reporter = IndexReporter(index="FIDETH", report_date=report_date, hour=self.hour)
        close_report = fidbtc_reporter.create_close_report()
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDETH Index Report for {report_date.strftime('%Y-%m-%d')}"
        file_name = f"FIDBTC-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class FidelityFIDETHMonitorReport(AbstractFidelityFIDETHMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)


class FidelityFIDETHMonitorReport3pm(AbstractFidelityFIDETHMonitorReport):
    """
    This report will just be sent to the <NAME_EMAIL> as well as the slack channel
    #alert-md-reports. Historically, this is ran at 3pm each day, so that if there is an issue with the numbers, it will
    show up in the alert channel and we can correct it before 4pm
    """

    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 15)


class FidelityFIDETHMonitorEarlyCloseReport(AbstractFidelityFIDETHMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 13)

    def run_report(self) -> bool:
        return self.run_report_for_early_close()


class AbstractFidelityFIDBCRMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("BCR", customer_report, folder_location, hour)

    def generate_report(self) -> ReportResults:
        """
        There's no files to send in this report so it will not attach any files
        """
        report_date = self.get_report_date()
        fidbcr_reporter = IndexReporter(index="FIDBCR", report_date=report_date, hour=self.hour)
        close_report = fidbcr_reporter.create_close_report()
        file_name = f"FIDBCR-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDBCR Index Report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class FidelityFIDBCRMonitorReport(AbstractFidelityFIDBCRMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)


class FidelityFIDBCRMonitorReport3pm(AbstractFidelityFIDBCRMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 15)


class FidelityFIDBCRMonitorEarlyCloseReport(AbstractFidelityFIDBCRMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 13)

    def run_report(self) -> bool:
        return self.run_report_for_early_close()


class AbstractFidelityFIDERRMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("ERR", customer_report, folder_location, hour)

    def generate_report(self) -> ReportResults:
        """
        There's no files to send in this report so it will not attach any files
        """
        report_date = self.get_report_date()
        index_reporter = IndexReporter(index="FIDERR", report_date=report_date, hour=self.hour)
        close_report = index_reporter.create_close_report()
        file_name = f"FIDERR-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDERR Index Report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class FidelityFIDERRMonitorReport(AbstractFidelityFIDERRMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)


class FidelityFIDERRMonitorEarlyCloseReport(AbstractFidelityFIDERRMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 13)

    def run_report(self) -> bool:
        return self.run_report_for_early_close()


class AbstractFidelityFIDSOLMonitorReport(AbstractFidelityFIDMonitorReport):

    def __init__(self, customer_report: CustomerReport, folder_location: str, hour=16):
        super().__init__("SOL", customer_report, folder_location, hour)

    def generate_report(self) -> ReportResults:
        """
        There's no files to send in this report so it will not attach any files
        """
        report_date = self.get_report_date()
        reporter = IndexReporter(index="FIDSOL", report_date=report_date, hour=self.hour)
        close_report = reporter.create_close_report()
        file_name = f"FIDSOL-fidelity-4pm-{report_date}.txt"
        backup_file = self.create_txtfile_backup(report_text=close_report, file_name=file_name)
        email_body = self.paragraph_to_html(close_report)
        email_header = f"FIDSOL Index Report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body, files_to_backup=[backup_file])


class FidelityFIDSOLMonitorReport(AbstractFidelityFIDSOLMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location)


class FidelityFIDSOLMonitorEarlyCloseReport(AbstractFidelityFIDSOLMonitorReport):
    def __init__(self, customer_report: CustomerReport, folder_location: str):
        super().__init__(customer_report, folder_location, 13)

    def run_report(self) -> bool:
        return self.run_report_for_early_close()


class FidelityNYCloseBTCIndexComparisonReport(ReportGenerator):
    """
    This report sends a body of text of a Fidelity index comparison
    to <EMAIL> each day at NY close time + 30 minutes.
    """

    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()
        reporter = FidelityNYCloseCompareReporter(report_date=report_date)
        email_body = self.paragraph_to_html(reporter.generate_full_report())
        email_header = f"Fidelity BTC Index Comparison Report for America/New_York on {reporter.get_4pm_ny_report_header()}"
        return ReportResults(result_files=[], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body)


class FidelityCloseExportNYReport(ReportGenerator):
    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()
        reporter = FidelityNYClosePriceExporter(folder_location=self.folder_location, report_date=report_date)
        result_file = reporter.generate_report()
        return ReportResults(result_files=[result_file], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class FidelityCloseExportLondonReport(ReportGenerator):
    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_london()
        reporter = FidelityLondonClosePriceExporter(folder_location=self.folder_location, report_date=report_date)
        result_file = reporter.generate_report()
        return ReportResults(result_files=[result_file], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class DDACmbiLondonCloseReport(ReportGenerator):
    """
    This reports creates a csv of CMBI index prices at london close each day
    """

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_london()
        london_close_file = create_dda_cmbi_london_close_file(report_date=report_date,
                                                              folder_location=self.folder_location)
        return ReportResults(result_files=[london_close_file], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=self.timely_report_name)


class GlobalXDailyIndexesReport(ReportGenerator):
    """
    This reports sends a body of text of a list of assets to global x each day at 11am est
    """

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_london()
        reporter = GlobalXReporter(assets_to_report_on=GlobalXReporter.REPORT_ASSSETS, report_date=report_date)
        email_body = self.paragraph_to_html(reporter.generate_full_report())
        email_header = f"{reporter.get_4pm_london_report_header()} - Coin Metrics End of Day prices"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body)


class CMBILondonCloseIndexesReport(ReportGenerator):
    """
    This reports sends a body of text of a list of assets to global x each day at 11am est
    """

    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_london()
        reporter = CMBILondonCloseReporter(assets=CMBILondonCloseReporter.REPORT_ASSETS, report_date=report_date)
        email_body = self.paragraph_to_html(reporter.generate_full_report())
        email_header = f"CMBI Index Levels for Europe/London on - {reporter.get_4pm_london_report_header()}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body)


class CMBINYCloseIndexesReport(ReportGenerator):
    """
    This report sends a body of text of a list of CMBI <NAME_EMAIL> each day at NY close time.
    """

    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()
        reporter = CMBINYCloseReporter(report_date=report_date)
        email_body = self.paragraph_to_html(reporter.generate_full_report())
        email_header = f"CMBI Index Levels for America/New_York on {reporter.get_4pm_ny_report_header(with_sec=True)}"
        return ReportResults(result_files=[], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body)


class CMBISingaporeIndexesReport(ReportGenerator):
    SEND_PROD_TO_ALERTS_MD = True

    def generate_report(self) -> ReportResults:
        report_date = datetime.date.today() if datetime.datetime.now(
            pytz.timezone("Asia/Singapore")).hour >= 17 else datetime.date.today() - datetime.timedelta(days=1)
        reporter = CMBISingaporeCloseReporter(report_date=report_date)
        email_body = self.paragraph_to_html(reporter.generate_full_report())
        email_header = f"CMBI Bitcoin Index Levels for Asia/Singapore on - {reporter.get_4pm_singapore_report_header()}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_body)


class OspreyReportGenerator(ReportGenerator):

    def run_report(self) -> bool:
        report_id = self.customer_report.pk
        report_date = self.get_report_date_4pm_est()
        if not report_date.weekday() <= 4:
            if is_local() or is_staging():
                log.info(f"report id: {report_id}, {self.__class__.__name__} run_report: "
                         f"returning always True since we are in LOCAL or STAGING")
                return True
            return False
        if not self.customer_report.delivery_method == "email":
            if is_local() or is_staging():
                log.info(f"report id: {report_id}, {self.__class__.__name__} run_report: "
                         f"returning always True since we are in LOCAL or STAGING")
                return True
            log.warning(f"report id: {report_id}, not running Osprey report, it is only eligible to send over email")
            return False
        return True

    def generate_report(self) -> ReportResults:
        report_date = self.get_report_date_4pm_est()
        reporter = OspreyEmailReport(report_date=report_date)
        email_body = reporter.generate_full_report()
        email_html = self.paragraph_to_html(email_body)
        email_header = f"CMBI Index report for {report_date.strftime('%Y-%m-%d')}"
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_html)


class MonthlyMetricsFileReport(ReportGenerator):
    """
    Generates Monthly Metrics File report for fireblocks.
    The report is a monthly asset metrics summary, just a single file.
    """

    def generate_report(self) -> ReportResults:
        reporter = MonthlyMetricsFile()
        result_file_name = reporter.generate_monthly_metrics_file(folder_location=self.folder_location)
        email_header = reporter.generate_email_header()
        email_body = reporter.generate_email_body()
        email_html = self.paragraph_to_html(email_body)

        return ReportResults(result_files=[result_file_name], customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name=email_header, email_body=email_html)


class MonitoringTaskReport(ReportGenerator):

    def generate_report(self) -> ReportResults:
        report_id = self.customer_report.pk
        log.debug(f"report id: {report_id}, running MonitoringTaskReport")
        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name="", email_body="")

class ClearOldWorkloadsReport(ReportGenerator):

    def generate_report(self) -> ReportResults:
        """
        Task to clear CustomerReportWorkload records older than the specified number of days
        """
        report_id = self.customer_report.pk
        log.debug(f"report id: {report_id}, running ClearOldWorkloadsReport")
        days = 30
        log.info(f"report id: {report_id}, invoking management command to delete workloads older than {days} days")
        call_command('clearworkloads', days=days)
        log.info(f"report id: {report_id}, completed workload cleanup")

        return ReportResults(customer_report=self.customer_report,
                             customer_report_workload=self.report_workload, report_status="REPORT GENERATED",
                             friendly_report_name="", email_body="")
