import datetime

import numpy as np
import pytz
from django.test import TestCase

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_constituents_report import \
    FidelityConstituentReportMultiAssetPair, get_report_date
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()
"""
This file is largely testing that values produce by python implementation match spreadsheet with relevant formulas 
for fidelity constituents https://docs.google.com/spreadsheets/d/1KS-6AWMxKJRQ5jMoMdgQLYxjYzcMn62B/edit#gid=1137878438
"""

date_1_1 = datetime.date(year=2023, day=1, month=1)
FIDBEIP_BTC_1_1 = FidelityConstituentReportMultiAssetPair(index="FIDBEIP", index_full_name="Fidelity Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDBTCP",
                                                          reporting_date=date_1_1, underlying_asset="btc")
FIDBEIP_ETH_1_1 = FidelityConstituentReportMultiAssetPair(index="FIDBEIP", index_full_name="Fidelity Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDETHP",
                                                          reporting_date=date_1_1, underlying_asset="eth")

date_1_22 = datetime.date(year=2023, month=1, day=22)
FIDEBET_ETH_1_22 = FidelityConstituentReportMultiAssetPair(index="FIDEBET", index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Total Return Index",
                                                          underlying_price_index="FIDETHP",
                                                          reporting_date=date_1_22, underlying_asset="eth")
FIDEBET_BTC_1_22 = FidelityConstituentReportMultiAssetPair(index="FIDEBET", index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Total Return Index",
                                                          underlying_price_index="FIDBTCP",
                                                          reporting_date=date_1_22, underlying_asset="btc")


date_1_23 = datetime.date(year=2023, month=1, day=23)
FIDBEIT_BTC_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDBEIT", index_full_name="Fidelity Bitcoin and Ethereum Total Return Index",
                                                           underlying_price_index="FIDBTCP",
                                                           reporting_date=date_1_23, underlying_asset="btc")
FIDBEIT_ETH_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDBEIT",index_full_name="Fidelity Bitcoin and Ethereum Total Return Index",
                                                           underlying_price_index="FIDETHP",
                                                           reporting_date=date_1_23, underlying_asset="eth")

FIDBEIP_BTC_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDBEIP", index_full_name="Fidelity Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDBTCP",
                                                          reporting_date=date_1_23, underlying_asset="btc")
FIDBEIP_ETH_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDBEIP", index_full_name="Fidelity Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDETHP",
                                                          reporting_date=date_1_23, underlying_asset="eth")


FIDEBEP_BTC_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDEBEP", index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDBTCP",
                                                          reporting_date=date_1_23, underlying_asset="btc")
FIDEBEP_ETH_1_23 = FidelityConstituentReportMultiAssetPair(index="FIDEBEP", index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Price Index",
                                                          underlying_price_index="FIDETHP",
                                                          reporting_date=date_1_23, underlying_asset="eth")

class TestConsituentCalculations(TestCase):

    def test_splyff_btc_12_1_2022(self) -> None:
        """
        Tests we get the right SplyFF for BTC ON 12/1/2022 - this is data capture date
        """
        expected_splyff_btc = 14195680.43198679
        actual_splyff_btc = FIDBEIP_BTC_1_1.get_splyff()
        expected_splyff_eth = 110897354.1824
        actual_splyff_eth = FIDBEIP_ETH_1_1.get_splyff()
        assert np.round(expected_splyff_btc, decimals=8) == np.round(actual_splyff_btc, decimals=8)
        assert np.round(expected_splyff_eth, decimals=4) == np.round(actual_splyff_eth, decimals=4)


    def test_asset_return1d(self) -> None:
        """
        Tests return1d calculations work as expected
        """
        expected_btc_asset_1d = 0.0238802803372142
        expected_eth_asset_1d = 0.0108926657289969
        actual_btc_asset_1d = FIDBEIT_BTC_1_23.get_asset_return1d()
        actual_eth_asset_1d = FIDBEIT_ETH_1_23.get_asset_return1d()

        assert expected_btc_asset_1d == np.round(actual_btc_asset_1d, decimals=len(str(238802803372142)) + 1)
        assert expected_eth_asset_1d == np.round(actual_eth_asset_1d, decimals=len(str(108926657289969)) + 1)


    def test_market_cap_jan_23(self) -> None:
        """
        Tests that market cap calculations work as expected
        """
        expected_marketcap_btc = 326076908874.80
        expected_marketcap_eth = 180930142322.20
        actual_marketcap_btc = FIDBEIT_BTC_1_23.get_marketcap()
        actual_marketcap_eth = FIDBEIT_ETH_1_23.get_marketcap()
        assert expected_marketcap_btc == np.round(actual_marketcap_btc, decimals=1)
        assert expected_marketcap_eth == np.round(actual_marketcap_eth, decimals=1)


    def test_index_shares_fidbeip_jan_2023(self) -> None:
        """
        Tests that the index shares calculations work as expected for btc and eth
        """
        btc_index_shares = FIDBEIP_BTC_1_23.get_index_shares()
        eth_index_shares = FIDBEIP_ETH_1_23.get_index_shares()
        expected_btc = 0.213077836528293
        expected_eth = 1.66457454569507
        assert expected_btc == np.round(btc_index_shares, decimals=len(str(213077836528293)))
        assert expected_eth == np.round(eth_index_shares, decimals=len(str(66457454569507)))


    def test_index_shares_fidebep_jan_2023(self) -> None:
        """
        Tests that the index shares calculations work as expected for btc and eth
        """
        btc_index_shares = FIDEBEP_BTC_1_23.get_index_shares()
        eth_index_shares = FIDEBEP_ETH_1_23.get_index_shares()
        expected_btc = 3.0676444424444
        expected_eth = 40.77433439053
        assert expected_btc == np.round(btc_index_shares, decimals=len(str(2130778365282)))
        assert expected_eth == np.round(eth_index_shares, decimals=len(str(66457454569)))


    def test_index_shares_fidebet_jan_2023(self) -> None:
        btc_index_shares = FIDEBET_BTC_1_22.get_index_shares()
        eth_index_shares = FIDEBET_ETH_1_22.get_index_shares()
        expected_btc = 3.06764444244446
        expected_eth = 40.774334390531
        assert expected_btc == np.round(btc_index_shares, decimals=len(str(6764444244445)) + 1)
        assert expected_eth == np.round(eth_index_shares, decimals=len(str(774334390530)))

    def test_get_index_level_jan_23(self) -> None:
        fidethp_level = FIDBEIT_ETH_1_23.get_asset_price()
        expected_fidethp = 1631.51
        fidbtcp_level = FIDBEIT_BTC_1_23.get_asset_price()
        expected_fidbtcp = 22970.15
        assert expected_fidbtcp == fidbtcp_level
        assert expected_fidethp == fidethp_level

    def test_utc_to_america_new_york(self) -> None:
        after_4pm_america_nyc = datetime.datetime(year=2023, month=3, day=15, hour=16, minute=5,
                                                  tzinfo=pytz.timezone("America/New_York"))
        expected_date = after_4pm_america_nyc.date()
        actual_date = get_report_date(after_4pm_america_nyc, hour_cutoff=16)
        assert expected_date == actual_date

    def test_report_date_before_4pm(self) -> None:
        before_4pm = datetime.datetime(year=2023, month=3, day=15, hour=15, minute=30,
                                       tzinfo=pytz.timezone("America/New_York"))
        expected_date = before_4pm.date() - datetime.timedelta(days=1)
        actual_date = get_report_date(before_4pm, hour_cutoff=16)
        assert expected_date == actual_date
