import os.path
import os.path
import re
from typing import Type
from unittest import skipIf

from django.test import TestCase
from django_celery_beat.models import CrontabSchedule

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.oauth_utils import \
    upload_to_google_drive
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.common import REPORT_NAME_TO_GENERATOR_MAP, ReportGenerator, \
    FILE_BACKUP_LOCATION
from coinmetrics_data_pusher.customer_reporting.models import FriendlyScheduleName, CustomerReport, Customer
from coinmetrics_data_pusher.customer_reporting.report_generators import ReportResults

log = LoggerFactory.get_logger("CMTestCase")


class CMTestCase(TestCase):
    GOOGLE_DRIVE_FOLDER_ID_TEST = "1NktHBdQEJk8cyO7EMf4j9UA8IIH1SeHe"

    @classmethod
    def setUpTestData(cls) -> None:
        """
        Sets up a test customer, as well as test schedule.
        """
        log.info("Setting up test data")
        customer = Customer.objects.create(customer_name="Test Customer")
        crontab_schedule = CrontabSchedule.objects.create(minute="4", hour=16, timezone="America/New_York")
        friendly_schedule_interval: FriendlyScheduleName = FriendlyScheduleName.objects.create(
            name_of_schedule="Crontab Sched", crontab=crontab_schedule)
        customer_report: CustomerReport = CustomerReport.objects.create(schedule=friendly_schedule_interval,
                                                                        customer=customer, delivery_method="email",
                                                                        report_name="Test report")

    def upload_to_google_drive_test_folder(self, report_results: ReportResults) -> None:
        """
        This method uploads the files from a report result to our google drive, rather than send to a customer
        """
        if report_results.result_files:
            for file in report_results.result_files:
                with open(file, 'r') as f:
                    data = f.read()
                file_name = file.split("/")[-1]
                log.info(f"Uploading file: {file_name} to google drive")
                upload_to_google_drive(self.GOOGLE_DRIVE_FOLDER_ID_TEST, name=f"test_{file_name}", data=data)
        elif report_results.email_body:
            html_remover = re.compile('<.*?>')
            email_html_removed = re.sub(html_remover, '', report_results.email_body)
            file_name = f"test_{'_'.join(report_results.customer_report.report_name.split(' '))}"
            full_file_location = os.path.join(FILE_BACKUP_LOCATION, file_name)
            with open(full_file_location, 'w') as test_file:
                test_file.write(email_html_removed)
            data = open(full_file_location).read()
            log.info(f"Uploading file: {file_name} to google drive")
            upload_to_google_drive(self.GOOGLE_DRIVE_FOLDER_ID_TEST, file_name, data=data)

    def run_customer_report_integration_test(self, report_name: str) -> ReportResults:
        log.info(f"Running integration test for report: {report_name}")
        customer_report = self.create_test_customer_report(report_name)
        report_generator_class: Type[ReportGenerator] = REPORT_NAME_TO_GENERATOR_MAP[
            customer_report.report_name]
        report_generator = report_generator_class(customer_report=customer_report,
                                                  folder_location=FILE_BACKUP_LOCATION)
        report_generator.BACKUP_REPORTS = False
        report_results = report_generator.generate_full_report()
        log.info(f"Integration test for report: {customer_report.report_name} ran succesfully")
        self.upload_to_google_drive_test_folder(report_results)
        return report_results

    @staticmethod
    def create_test_customer_report(report_name: str) -> CustomerReport:
        """
        Creates a test CustomerReport object for a given report name. If you don't pass in a valid name
        from the REPORTS_GENERATOR_MAP will throw value error
        """
        customer: Customer = Customer.objects.get(pk=1)
        friendly_schedule: FriendlyScheduleName = FriendlyScheduleName.objects.get(pk=1)
        new_customer_report: CustomerReport = CustomerReport.objects.create(schedule=friendly_schedule,
                                                                            customer=customer,
                                                                            delivery_method="sftp_private_key_auth",
                                                                            report_name=report_name)
        return new_customer_report

class TestReportGeneratorsIntegrationTests(CMTestCase):
    """
    This test case is meant to run all the reports we have set up as integration tests. Rather than send to any real
    customer - it will just upload files to a google drive folder
    """


    def test_cmbi_btc_eth_sftp_reports(self) -> None:
        reports = ["CMBI Export ETH Files", "CMBI Export BTC Files"]
        for report in reports:
            self.run_customer_report_integration_test(report)

    def test_grayscale_sftp_reports(self) -> None:
        reports = ["Grayscale Assets Report"]
        for report in reports:
            self.run_customer_report_integration_test(report)

    def test_cmbi_monitor_reports(self) -> None:
        reports = ["CMBI London Close Report",
                   "CMBI London Close Report Email",
                   "CMBI Singapore Close Indexes Report Email",
                   "GlobalX Indexes Report Email"]
        for report in reports:
            self.run_customer_report_integration_test(report)


class TestCMMonthlyReports(CMTestCase):

    def test_monthly_reports(self) -> None:
        """
        Tests the monthly reports we have - end of month CIV report and Monthly Reference Rates Report (Atlas Fund)
        Test Reports "Monthly Reference Rates Report", "CIV Last Day of Month RR"
        """
        reports = ["Monthly Reference Rates Report", "CIV Last Day of Month RR"]
        for report in reports:
            self.run_customer_report_integration_test(report)


class TestFidelityReports(CMTestCase):

    def test_fidelity_daily_sftp_reports(self) -> None:
        """
        This tests all the daily SFTP reports we send to fidelity_bitcoin_index, excluding the constituents reports
        which we will have in a separate test:
        """
        reports = ["Fidelity Daily BTC Index Report",
                   "Fidelity Daily ETH Index Report",
                   "Fidelity Daily Even Weighted BTC ETH Index Report",
                   "Fidelity Cap Weighted BTC ETH Index Report",
                   "Fidelity Combined Daily Indexes Report"
                   ]
        for report in reports:
            self.run_customer_report_integration_test(report)

    def test_3pm_fidelity_sftp_reports(self):
        report = "Fidelity 3pm Combined Realtime Indexes Report"
        self.run_customer_report_integration_test(report)

    def test_4pm_fidelity_sftp_reports(self):
        report = "Fidelity 4pm Combined Realtime Indexes Report"
        self.run_customer_report_integration_test(report)


class TestFidelityConstituentReports(CMTestCase):
    """
    Runs the constituents reports
    """
    def test_fidelity_constituent_report_normal(self) -> None:
        """
        Tests fidelity constituent reports
        """
        report = "Fidelity 4pm Constituents Report"
        self.run_customer_report_integration_test(report)

    def test_fidelity_constituent_report_proforma(self) -> None:
        """
        Tests fidelity constituent reports
        """
        report = "Fidelity Proforma Constituents Report"
        self.run_customer_report_integration_test(report)


class TestDatabaseReports(CMTestCase):
    """
    These tests all require a test postgresql database, which hasn't been created yet.
    """

    @skipIf(True, reason="Skipping this test for now because it requires database access")
    def test_osprey_reports(self) -> None:
        reports = ["Osprey Daily CMBI Email"]
        for report in reports:
            self.run_customer_report_integration_test(report)

    @skipIf(True, reason="Skipping this test for now because it requires database access")
    def test_fidelity_monitor_reports(self) -> None:
        reports = ["Fidelity Close Monitor FIDBTC Report", "Fidelity Close Monitor FIDBTC Early Close Report",
                   "Fidelity Close Monitor FIDETH Report", "Fidelity Close Monitor FIDETH Early Close Report"]
        for report in reports:
            self.run_customer_report_integration_test(report)

    @skipIf(True, reason="Skipping this test for now - has dependency on candle maker")
    def test_dda_momentum_rebalance_report(self) -> None:
        """
        Tests dda daily report
        """
        report = "DDA Momentum/ Rebalance Report"
        self.run_customer_report_integration_test(report)
