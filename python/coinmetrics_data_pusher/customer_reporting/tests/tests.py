from datetime import datetime as dt

from django.test import TestCase
from django_celery_beat.models import IntervalSchedule, ClockedSchedule, CrontabSchedule

from coinmetrics_data_pusher.customer_reporting.models import FriendlyScheduleName, Customer<PERSON><PERSON>ort, Customer, \
    PeriodicTask
from datetime import datetime as dt

from django.test import TestCase
from django_celery_beat.models import IntervalSchedule, ClockedSchedule, CrontabSchedule

from coinmetrics_data_pusher.customer_reporting.models import FriendlyScheduleName, CustomerReport, Customer, \
    PeriodicTask


class FriendlyScheduleTests(TestCase):
    """
    Tests for the Friendly Schedule class
    """

    def test_get_schedule(self) -> None:
        """
        Tests that the get schedule method works as expected - it will return the non null schedule
        """
        interval_sched = IntervalSchedule.objects.create(every=30, period="seconds")
        clocked_sched = ClockedSchedule.objects.create(clocked_time=dt.now())
        friendly_schedule_interval: FriendlyScheduleName = FriendlyScheduleName.objects.create(
            name_of_schedule="Interval Sched", interval=interval_sched)
        friendly_schedule_clocked: FriendlyScheduleName = FriendlyScheduleName.objects.create(
            name_of_schedule="Interval Sched", clocked=clocked_sched)
        self.assertEqual(interval_sched, friendly_schedule_interval.get_schedule()['interval'])
        self.assertEqual(clocked_sched, friendly_schedule_clocked.get_schedule()['clocked'])

    def test_only_one_schedule_type_allowed(self):
        """
        When creating a friendly schedule there should only be one schedule allowed, tests that you can't create a
        schedule with multiple or none
        """
        interval_sched = IntervalSchedule.objects.create(every=30, period="seconds")
        clocked_sched = ClockedSchedule.objects.create(clocked_time=dt.now())
        # Workaround due to self.assertRaises behaviour
        try:
            FriendlyScheduleName.objects.create(name_of_schedule="too many schedules", interval=interval_sched,
                                                clocked=clocked_sched)
            self.assertTrue(False, msg="The above create object statement should throw an AssertionError, failing")
        except AssertionError:
            self.assertTrue(True)
        try:
            FriendlyScheduleName.objects.create(name_of_schedule="No schedule", )
            self.assertTrue(False, msg="The above create object statement should throw an AssertionError, failing")
        except AssertionError:
            self.assertTrue(True)


class TestCreateCustomerReports(TestCase):
    """
    Tests logic around creating customer reports. Customer reports are meant to be an abstraction over the
    django_celery_beat model PeriodicTask, so this is mainly testing that the interaction between them works as
    expected
    """

    def create_example_crontab_schedule_cr(self) -> CustomerReport:
        customer = Customer.objects.create(customer_name="Test Customer")
        crontab_schedule = CrontabSchedule.objects.create(minute="4", hour=16, timezone="America/New_York")
        friendly_schedule_interval: FriendlyScheduleName = FriendlyScheduleName.objects.create(
            name_of_schedule="Interval Sched", crontab=crontab_schedule)
        customer_report: CustomerReport = CustomerReport.objects.create(schedule=friendly_schedule_interval,
                                                                        customer=customer, delivery_method="email",
                                                                        report_name="Test report")
        return customer_report

    def test_creating_customer_report(self) -> None:
        """
        Tests that when we make a CustomerReport a related PeriodicTask object is created as expected
        """
        customer_report = self.create_example_crontab_schedule_cr()
        latest_task: PeriodicTask = PeriodicTask.objects.latest("id")
        self.assertEqual(latest_task,
                         customer_report.periodic_task)  # Checks after save that the periodic task is linked to the custmoer report
        self.assertEqual(latest_task.kwargs,
                         f'{{"customer_report_id": {customer_report.id} }}')  # Checks the customer report id is passed down correctly

    def test_that_related_periodic_task(self) -> None:
        """
        The CustomerReport model is a wrapper over the PeriodicTask model, so when a CustomerReport object is deleted,
        the related PeriodicTask object should also be deleted
        """
        customer_report = self.create_example_crontab_schedule_cr()
        related_task: PeriodicTask = PeriodicTask.objects.latest("id")
        related_task_id = related_task.id
        query_for_related_task = PeriodicTask.objects.filter(id=related_task_id)
        self.assertEqual(len(query_for_related_task), 1)
        self.assertEqual(related_task.kwargs,
                         f'{{"customer_report_id": {customer_report.id} }}')  # Checks that the tasks are in fact related
        customer_report.delete()
        query_for_related_task = PeriodicTask.objects.filter(id=related_task_id)
        self.assertEqual(len(query_for_related_task), 0)
