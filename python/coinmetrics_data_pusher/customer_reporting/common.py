from typing import Dict, Type

from coinmetrics_data_pusher.customer_reporting.report_generators import (
    ReportGenerator,
    AtlasFundReport,
    CastleIslandEomReport,
    FidelityDailyBTCIndexReport,
    FidelityDailyETHIndexReport,
    FidelityDailyBTCETHEvenReport,
    FidelityCombinedDailyIndexReports,
    FidelityDailyBTCETHCapWeightedReport,
    Fidelity3PMRealTimeCombinedIndexReports,
    Fidelity4PMRealTimeCombinedIndexReports,
    FidelityConstituentsReport,
    FidelityProformaConstituentReport,
    CMBIExportBTCFiles,
    CMBIExportETHFiles,
    GrayscaleAssetsReport,
    DataBaseBackupReport,
    FidelityFIDBTCMonitorReport,
    FidelityFIDETHMonitorReport,
    DDACmbiLondonCloseReport,
    CMBILondonCloseIndexesReport,
    CMBISingaporeIndexesReport,
    GlobalXDailyIndexesReport,
    OspreyReportGenerator,
    FidelityFIDBTCMonitorEarlyCloseReport,
    FidelityFIDETHMonitorEarlyCloseReport,
    FidelityFIDETHMonitorReport3pm,
    FidelityFIDBTCMonitorReport3pm, CMBINYCloseIndexesReport, FidelityNYCloseBTCIndexComparisonReport,
    FidelityDailyBTCRRIndexReport,
    FidelityFIDBCRMonitorReport, FidelityFIDBCRMonitorEarlyCloseReport, FidelityFIDBCRMonitorReport3pm,
    FidelityCloseExportNYReport, FidelityCloseExportLondonReport, FidelityDailyETHRRIndexReport,
    FidelityFIDERRMonitorReport, FidelityFIDERRMonitorEarlyCloseReport, MonitoringTaskReport,
    FidelityDailyBTCIndexLondonReport, FidelityFIDBTCLMonitorReport, MonthlyMetricsFileReport,
    FidelityFIDSOLMonitorReport, FidelityFIDSOLMonitorEarlyCloseReport, FidelityDailySOLRRIndexReport,
    ClearOldWorkloadsReport
)

REPORT_NAME_TO_GENERATOR_MAP: Dict[str, Type[ReportGenerator]] = {
    "Monthly Reference Rates Report": AtlasFundReport,
    "Monthly Metrics File Report": MonthlyMetricsFileReport,
    "CIV Last Day of Month RR": CastleIslandEomReport,
    "Fidelity Daily BTC Index Report": FidelityDailyBTCIndexReport,
    "Fidelity Daily BTC Index London Report": FidelityDailyBTCIndexLondonReport,
    "Fidelity Daily ETH Index Report": FidelityDailyETHIndexReport,
    "Fidelity Daily BTC RR Index Report": FidelityDailyBTCRRIndexReport,
    "Fidelity Daily ETH RR Index Report": FidelityDailyETHRRIndexReport,
    "Fidelity Daily SOL RR Index Report": FidelityDailySOLRRIndexReport,
    "Fidelity Daily Even Weighted BTC ETH Index Report": FidelityDailyBTCETHEvenReport,
    "Fidelity Cap Weighted BTC ETH Index Report": FidelityDailyBTCETHCapWeightedReport,
    "Fidelity Combined Daily Indexes Report": FidelityCombinedDailyIndexReports,
    "Fidelity 3pm Combined Realtime Indexes Report": Fidelity3PMRealTimeCombinedIndexReports,
    "Fidelity 4pm Combined Realtime Indexes Report": Fidelity4PMRealTimeCombinedIndexReports,
    "Fidelity 4pm Constituents Report": FidelityConstituentsReport,
    "Fidelity Proforma Constituents Report": FidelityProformaConstituentReport,
    "CMBI Export ETH Files": CMBIExportETHFiles,
    "CMBI Export BTC Files": CMBIExportBTCFiles,
    "Grayscale Assets Report": GrayscaleAssetsReport,
    "Data Pusher Data Base Backup": DataBaseBackupReport,
    "Fidelity Close Export London Report": FidelityCloseExportLondonReport,
    "Fidelity Close Export NY Report": FidelityCloseExportNYReport,
    "Fidelity Close Monitor FIDBTC Report": FidelityFIDBTCMonitorReport,
    "Fidelity Close Monitor FIDBTC Early Close Report": FidelityFIDBTCMonitorEarlyCloseReport,
    "Fidelity Close Monitor FIDBTCL Report": FidelityFIDBTCLMonitorReport,
    "Fidelity Close Monitor FIDETH Report": FidelityFIDETHMonitorReport,
    "Fidelity Close Monitor FIDETH Early Close Report": FidelityFIDETHMonitorEarlyCloseReport,
    "Fidelity Close Monitor FIDBCR Report": FidelityFIDBCRMonitorReport,
    "Fidelity Close Monitor FIDBCR Early Close Report": FidelityFIDBCRMonitorEarlyCloseReport,
    "Fidelity Close Monitor FIDERR Report": FidelityFIDERRMonitorReport,
    "Fidelity Close Monitor FIDERR Early Close Report": FidelityFIDERRMonitorEarlyCloseReport,
    "Fidelity Close Monitor FIDSOL Report": FidelityFIDSOLMonitorReport,
    "Fidelity Close Monitor FIDSOL Early Close Report": FidelityFIDSOLMonitorEarlyCloseReport,
    "CMBI London Close Report": DDACmbiLondonCloseReport,
    "CMBI London Close Report Email": CMBILondonCloseIndexesReport,
    "CMBI NY Close Report Email": CMBINYCloseIndexesReport,
    "CMBI Singapore Close Indexes Report Email": CMBISingaporeIndexesReport,
    "GlobalX Indexes Report Email": GlobalXDailyIndexesReport,
    "Osprey Daily CMBI Email": OspreyReportGenerator,
    "Fidelity Close Monitor FIDBTC Report 3pm Check": FidelityFIDBTCMonitorReport3pm,
    "Fidelity Close Monitor FIDETH Report 3pm Check": FidelityFIDETHMonitorReport3pm,
    "Fidelity Close Monitor FIDBCR Report 3pm Check": FidelityFIDBCRMonitorReport3pm,
    "Fidelity NY Close BTC Index Comparison Report": FidelityNYCloseBTCIndexComparisonReport,
    "Monitoring Task Report": MonitoringTaskReport,
    "Clear Old Workloads Report": ClearOldWorkloadsReport
}

FILE_BACKUP_LOCATION = "/tmp/data"
