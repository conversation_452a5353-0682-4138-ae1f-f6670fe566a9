from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction


class Command(BaseCommand):
    help = 'Deactivate users instead of deleting them (safer approach)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            required=True,
            help='Email of the user to deactivate',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--reactivate',
            action='store_true',
            help='Reactivate the user instead of deactivating',
        )

    def handle(self, *args, **options):
        User = get_user_model()
        email = options['email']
        dry_run = options['dry_run']
        reactivate = options['reactivate']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        try:
            users = User.objects.filter(email=email)
            
            if not users.exists():
                self.stdout.write(self.style.ERROR(f'No users found with email: {email}'))
                return

            if users.count() > 1:
                self.stdout.write(self.style.WARNING(f'Found {users.count()} users with email {email}:'))
                for i, user in enumerate(users):
                    status = "ACTIVE" if user.is_active else "INACTIVE"
                    self.stdout.write(f'  {i+1}. ID: {user.id}, Username: {user.username}, Status: {status}')
                
                if not dry_run:
                    self.stdout.write(self.style.WARNING('Multiple users found. Please clean up duplicates first using cleanup_duplicate_users command.'))
                    return

            for user in users:
                current_status = "ACTIVE" if user.is_active else "INACTIVE"
                
                if reactivate:
                    if user.is_active:
                        self.stdout.write(f'User {email} is already active')
                        continue
                    
                    if dry_run:
                        self.stdout.write(f'WOULD REACTIVATE: {email} (ID: {user.id})')
                    else:
                        with transaction.atomic():
                            user.is_active = True
                            user.save()
                            self.stdout.write(self.style.SUCCESS(f'REACTIVATED: {email} (ID: {user.id})'))
                else:
                    if not user.is_active:
                        self.stdout.write(f'User {email} is already inactive')
                        continue
                    
                    if dry_run:
                        self.stdout.write(f'WOULD DEACTIVATE: {email} (ID: {user.id})')
                    else:
                        with transaction.atomic():
                            user.is_active = False
                            user.save()
                            self.stdout.write(self.style.SUCCESS(f'DEACTIVATED: {email} (ID: {user.id})'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error processing user {email}: {str(e)}'))

        if dry_run:
            action = "reactivation" if reactivate else "deactivation"
            self.stdout.write(self.style.WARNING(f'Dry run completed. Run without --dry-run to perform {action}.'))
        else:
            action = "reactivated" if reactivate else "deactivated"
            self.stdout.write(self.style.SUCCESS(f'User {action} completed!'))
