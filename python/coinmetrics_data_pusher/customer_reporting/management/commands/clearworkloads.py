import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from coinmetrics_data_pusher.customer_reporting.models import CustomerReportWorkload
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("ClearWorkloads")

class Command(BaseCommand):
    help = 'Deletes CustomerReportWorkload records older than 30 days'

    def add_arguments(self, parser):
        parser.add_argument('--days', type=int, default=30,
                            help='Number of days to keep workload records (default: 30)')

    def handle(self, *args, **options):
        days = options['days']
        cutoff_date = timezone.now() - datetime.timedelta(days=days)
        
        count, _ = CustomerReportWorkload.objects.filter(
            timestamp_created__lt=cutoff_date
        ).delete()
        
        log.info(f"Deleted {count} CustomerReportWorkload records older than {days} days")
        self.stdout.write(
            self.style.SUCCESS(f'Successfully deleted {count} workload records older than {days} days')
        )