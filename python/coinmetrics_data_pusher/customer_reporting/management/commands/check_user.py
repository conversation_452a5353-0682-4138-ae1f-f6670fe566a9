from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model


class Command(BaseCommand):
    help = 'Check details of users with a specific email'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            required=True,
            help='Email to check',
        )

    def handle(self, *args, **options):
        User = get_user_model()
        email = options['email']

        users = User.objects.filter(email=email)
        
        if not users.exists():
            self.stdout.write(self.style.ERROR(f'No users found with email: {email}'))
            return

        self.stdout.write(f'Found {users.count()} user(s) with email: {email}')
        self.stdout.write('-' * 80)

        for i, user in enumerate(users, 1):
            status = "ACTIVE" if user.is_active else "INACTIVE"
            self.stdout.write(f'User #{i}:')
            self.stdout.write(f'  ID: {user.id}')
            self.stdout.write(f'  Username: {user.username}')
            self.stdout.write(f'  Email: {user.email}')
            self.stdout.write(f'  Status: {status}')
            self.stdout.write(f'  Staff: {user.is_staff}')
            self.stdout.write(f'  Superuser: {user.is_superuser}')
            self.stdout.write(f'  Date Joined: {user.date_joined}')
            self.stdout.write(f'  Last Login: {user.last_login}')
            self.stdout.write(f'  Permissions: {user.user_permissions.count()}')
            self.stdout.write('')

        if users.count() > 1:
            self.stdout.write(self.style.WARNING('Multiple users detected!'))
            self.stdout.write('To fix this, you can:')
            self.stdout.write('1. Deactivate unwanted users:')
            self.stdout.write(f'   python manage.py deactivate_user --email {email}')
            self.stdout.write('2. Or clean up duplicates completely:')
            self.stdout.write(f'   python manage.py cleanup_duplicate_users --email {email}')
        else:
            self.stdout.write(self.style.SUCCESS('No duplicates found!'))
