from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from collections import defaultdict


class Command(BaseCommand):
    help = 'Find and clean up duplicate users with the same email address'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Clean up duplicates for a specific email address',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--keep-strategy',
            type=str,
            choices=['newest', 'oldest', 'most-permissions'],
            default='newest',
            help='Strategy for which user to keep when duplicates are found (default: newest)',
        )

    def handle(self, *args, **options):
        User = get_user_model()
        dry_run = options['dry_run']
        email = options['email']
        keep_strategy = options['keep_strategy']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Find duplicate users
        if email:
            users = User.objects.filter(email=email)
            if users.count() <= 1:
                self.stdout.write(self.style.SUCCESS(f'No duplicates found for email: {email}'))
                return
            duplicates = {email: list(users)}
        else:
            # Find all emails with duplicates
            duplicates = defaultdict(list)
            for user in User.objects.all():
                duplicates[user.email].append(user)
            
            # Keep only emails that have duplicates
            duplicates = {email: users for email, users in duplicates.items() if len(users) > 1}

        if not duplicates:
            self.stdout.write(self.style.SUCCESS('No duplicate users found'))
            return

        self.stdout.write(f'Found {len(duplicates)} email addresses with duplicate users')

        for email, users in duplicates.items():
            self.stdout.write(f'\nEmail: {email} has {len(users)} duplicate users:')
            
            for i, user in enumerate(users):
                self.stdout.write(f'  {i+1}. ID: {user.id}, Username: {user.username}, '
                                f'Created: {user.date_joined}, Staff: {user.is_staff}, '
                                f'Superuser: {user.is_superuser}, Permissions: {user.user_permissions.count()}')

            # Determine which user to keep
            if keep_strategy == 'newest':
                user_to_keep = max(users, key=lambda u: u.date_joined)
            elif keep_strategy == 'oldest':
                user_to_keep = min(users, key=lambda u: u.date_joined)
            elif keep_strategy == 'most-permissions':
                user_to_keep = max(users, key=lambda u: u.user_permissions.count())

            users_to_delete = [u for u in users if u.id != user_to_keep.id]

            self.stdout.write(f'  → Keeping user ID {user_to_keep.id} (strategy: {keep_strategy})')
            self.stdout.write(f'  → Will delete {len(users_to_delete)} duplicate(s)')

            if dry_run:
                for user in users_to_delete:
                    self.stdout.write(f'    WOULD DELETE: ID {user.id}, Username: {user.username}')
            else:
                try:
                    with transaction.atomic():
                        for user in users_to_delete:
                            self.stdout.write(f'    DELETING: ID {user.id}, Username: {user.username}')
                            user.delete()
                        self.stdout.write(self.style.SUCCESS(f'  ✓ Cleaned up duplicates for {email}'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'  ✗ Error cleaning up {email}: {str(e)}'))

        if dry_run:
            self.stdout.write(self.style.WARNING('\nDry run completed. Run without --dry-run to make actual changes.'))
        else:
            self.stdout.write(self.style.SUCCESS('\nDuplicate cleanup completed!'))
