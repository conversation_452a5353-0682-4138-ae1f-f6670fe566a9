import requests
import celery
from django.shortcuts import render
from django.db.models import QuerySet
from django.contrib import admin
from coinmetrics_data_pusher.customer_reporting.models import Customer, CustomerReport, CustomerReportWorkload, FriendlyScheduleName
from django.forms.widgets import Select
from coinmetrics_data_pusher.customer_reporting.common import REPORT_NAME_TO_GENERATOR_MAP
from django import forms
from typing import Tuple, Union, List
from django.contrib.admin import AdminSite


class ReportSelectWidget(Select):
    """Widget that lets you choose between task names."""

    _choices = None

    def reports_as_choices(self) -> Tuple[Tuple[str, str]]:
        tasks = list(sorted(name for name in REPORT_NAME_TO_GENERATOR_MAP.keys()
                            if not name.startswith('celery.')))
        return (('', ''), ) + tuple(zip(tasks, tasks))

    @property
    def choices(self):
        if self._choices is None:
            self._choices = self.reports_as_choices()
        return self._choices

    @choices.setter
    def choices(self, _):
        pass


class ReportChoiceField(forms.ChoiceField):
    """Field that lets you choose between task names."""

    widget = ReportSelectWidget

    def valid_value(self, value) -> bool:
        return True


class CustomerReportGenerationForm(forms.ModelForm):

    report_name = ReportChoiceField(
        label=('Report to generate'),
        required=True,
    )

    class Meta:
        model = CustomerReport
        exclude = ('periodic_task',)

    def clean(self):
        data = super().clean()
        return data


def delete_selected_reports(modelAdmin: admin.ModelAdmin, request: requests.Request, queryset: Union[QuerySet, List[CustomerReport]]) -> None:
    """
    This method deletes selected CustomerReport objects in the admin panel. Needed because the default method doesn't
    actually call the .delete() method, which causes issues.
    :param modelAdmin:
    :param request:
    :param queryset:
    :return:
    :rtype:
    """
    for customer_report in queryset:
        customer_report.delete()

delete_selected_reports.short_description = "Delete selected reports fully"

def trigger_reports(modelAdmin: admin.ModelAdmin,
                    request: requests.Request,
                    queryset: Union[QuerySet, List[CustomerReport]]
                    ) -> None:
    """
    Function for the admin interface, will trigger all the selected reports. Useful for testing
    :param modelAdmin:
    :param request:
    :param queryset:
    :return:
    """
    from coinmetrics_data_pusher.customer_reporting.tasks import generate_and_send_report
    for customer_report in queryset:
        generate_and_send_report(customer_report.id, MANUAL_TRIGGER=True)

trigger_reports.short_description = "Manually trigger reports"

class CustomerReportWorkloadAdmin(admin.ModelAdmin):
    """
    Admin interface for CustomerReportWorkload with table view and detailed popup
    """

    def get_report_name(self, obj):
        """Get the report name from the related CustomerReport"""
        return obj.customer_report.report_name if obj.customer_report else 'N/A'
    get_report_name.short_description = 'Report Name'
    get_report_name.admin_order_field = 'customer_report__report_name'

    def get_customer_name(self, obj):
        """Get the customer name from the related CustomerReport"""
        return obj.customer_report.customer.customer_name if obj.customer_report and obj.customer_report.customer else 'N/A'
    get_customer_name.short_description = 'Customer'
    get_customer_name.admin_order_field = 'customer_report__customer__customer_name'

    def get_delivery_method(self, obj):
        """Get the delivery method from the related CustomerReport"""
        return obj.customer_report.delivery_method if obj.customer_report else 'N/A'
    get_delivery_method.short_description = 'Delivery Method'
    get_delivery_method.admin_order_field = 'customer_report__delivery_method'

    def get_duration(self, obj):
        """Calculate task duration if both start and end times are available"""
        if obj.start_time_task and obj.end_time_task:
            duration = obj.end_time_task - obj.start_time_task
            return str(duration)
        return 'N/A'
    get_duration.short_description = 'Duration'

    def get_failure_message_short(self, obj):
        """Get a truncated version of the failure message for table display"""
        if obj.failure_message:
            return obj.failure_message[:100] + '...' if len(obj.failure_message) > 100 else obj.failure_message
        return 'N/A'
    get_failure_message_short.short_description = 'Failure Message (Preview)'

    # Table view configuration
    list_display = [
        'id',
        'get_report_name',
        'get_customer_name',
        'status',
        'timestamp_created',
        'get_duration',
        'get_failure_message_short'
    ]

    # Add filtering options
    list_filter = [
        'status',
        'timestamp_created',
        'customer_report__delivery_method',
        'customer_report__customer__customer_name'
    ]

    # Add search functionality
    search_fields = [
        'customer_report__report_name',
        'customer_report__customer__customer_name',
        'status',
        'failure_message'
    ]

    # Configure the detail view with organized sections
    fieldsets = (
        ('Basic Information', {
            'fields': ('customer_report', 'get_report_name', 'get_customer_name', 'get_delivery_method')
        }),
        ('Execution Results', {
            'fields': ('status', 'failure_message', 'files_delivered_successfully', 'files_delivered_failed',)
        }),
        ('Timing Information', {
            'fields': ('timestamp_created', 'updated', 'start_time_task', 'end_time_task')
        }),
        ('File Information', {
            'fields': ('location_of_file_backup',)
        })
    )

    # Make fields readonly
    readonly_fields = [
        'customer_report',
        'status',
        'timestamp_created',
        'updated',
        'start_time_task',
        'end_time_task',
        'files_delivered_successfully',
        'files_delivered_failed',
        'failure_message',
        'location_of_file_backup',
        'get_report_name',
        'get_customer_name',
        'get_delivery_method',

    ]

    # Order by most recent first
    ordering = ['-timestamp_created']

    # Show more items per page
    list_per_page = 50

class CustomerReportTaskAdmin(admin.ModelAdmin):
    """
    Admin interface for creating CustomerReport workflows
    """
    list_display = ["id", "customer", "periodic_task", "delivery_method", "timestamp_created", "updated", "report_name", "schedule", "notes"]
    form = CustomerReportGenerationForm
    model = CustomerReport
    actions = [delete_selected_reports, trigger_reports]

class CustomAdminSite(AdminSite):
    def login(self, request, extra_context=None):
        if request.user.is_authenticated:
            return super().login(request, extra_context)
        return render(request, "admin_login.html")

admin_site = CustomAdminSite(name="my_admin")


admin_site.register(Customer)
admin_site.register(CustomerReport, CustomerReportTaskAdmin)
admin_site.register(CustomerReportWorkload, CustomerReportWorkloadAdmin)
admin_site.register(FriendlyScheduleName)

from django.apps import apps

all_models = apps.get_models()

for model in all_models:
    try:
        admin.site.unregister(model)
    except admin.sites.NotRegistered:
        pass
    try:
        admin_site.register(model)
    except admin.sites.AlreadyRegistered:
        pass
