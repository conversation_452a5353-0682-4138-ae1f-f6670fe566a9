from __future__ import annotations

import datetime
import json
import os
from datetime import date
from typing import Dict
from zoneinfo import ZoneInfo

import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_date_resolver import FidelityDateResolver
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

client = CoinMetricsClientFactory.get_client()

DAILY_REPORT_START_TIME: date = datetime.date(year=2015, month=12, day=31)


class AbstractFidelityIndexPairReportGenerator:

    """
    dataclass to handle the relationship for fidelity assets - for each there is a price index and a total return index

    At a high level, there is a few aspects of building these reports for fidelity. We need to pull the price and return
    index levels at the requested time and frequency (either historical daily, or daily at 15s frequency), we need to take
    data and join it into the Dataframe structure that fidelity expects. Then, the last step is adding the "market_value"
    calculation where necessary. The market value calculation is fairly complex and technical, and is specialized per report
    to an extent. A lot of what this class structure does is try and abstract out the rest of that process so we can
    clearly add that market value calculation into code.
    """

    def __init__(self,
                 price_index: str,
                 return_index: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 start_date: datetime.date = DAILY_REPORT_START_TIME,
                 frequency: str = "1d-ny-close",
                 hour: int = 16,
                 timezone_name: str = "America/New_York"):
        self.price_index = price_index
        self.return_index = return_index
        self.reporting_date = reporting_date
        self.start_time_dt = datetime.datetime(year=start_date.year,
                                               month=start_date.month,
                                               day=start_date.day,
                                               hour=hour,
                                               tzinfo=ZoneInfo(timezone_name), ).astimezone(ZoneInfo("UTC"))
        self.end_time_dt = datetime.datetime(year=self.reporting_date.year,
                                             month=self.reporting_date.month,
                                             day=self.reporting_date.day,
                                             hour=hour,
                                             tzinfo=ZoneInfo(timezone_name), ).astimezone(ZoneInfo("UTC"))
        self.start_time_dt_str = self.start_time_dt.strftime("%Y-%m-%dT%H0000")
        self.end_time_dt_str = self.end_time_dt.strftime("%Y-%m-%dT%H0000")
        self.frequency = frequency
        self.hour = hour
        self.timezone_name = timezone_name

    def generate_realtime_index_report(self, folder_location: str, file_prefix: str) -> str:
        """
        Creates a report that contains returns at a 15s frequency for the entire days worth of prices for the
        relevant indexes.
        :param folder_location: location where the file will be created
        :param file_prefix: str prefix for the file. I.e. if 'fidbtc' is used that will be the prefix for the file used
        :return: str full path to the file generated
        """
        data_df = self.generate_realtime_index_report_df()
        file_name = f"{file_prefix}-{self.reporting_date.strftime('%Y-%m-%d')}T{self.hour}00-0400.csv"
        full_file_path = os.path.join(folder_location, file_name)
        data_df.to_csv(full_file_path, index=False)
        return full_file_path

    def generate_daily_index_report(self, folder_location: str, file_prefix: str) -> str:
        """
        Creates daily report in format <folder_location>/<file_prefix>-yyyy-mm-dd.csv. This file contains all historical
        1d-ny-close data or the relevant indexes as well as s "market_value" calculation specific to the indexes
        :param folder_location: folder location where file will be created
        :param file_prefix: str prefix for the file. I.e. if 'fidbtc' is used that will be the prefix for the file used
        :return: str full path to the file name created
        """
        data_df = self.generate_daily_index_report_df()
        full_file_path = os.path.join(folder_location, f"{file_prefix}-{self.reporting_date.strftime('%Y-%m-%d')}.csv")
        data_df.to_csv(full_file_path, index=False)
        return full_file_path

    def generate_daily_index_report_df(self) -> pd.DataFrame:
        """
        Generates a data frame for the daily index reports. Separate from main function for easy testing.
        """
        price_index_df = self.get_price_index_daily()
        return_index_df = self.get_return_index_daily()
        combined_index_df = self.transform_concat_price_return_dfs(price_df=price_index_df, return_df=return_index_df)
        combined_index_with_market_value = self.add_market_value_calculation(combined_index_df)
        combined_index_with_market_value = self.post_process_fidelity_dataframe(combined_index_with_market_value)
        combined_index_with_market_value = combined_index_with_market_value
        return combined_index_with_market_value

    def generate_realtime_index_report_df(self) -> pd.DataFrame:
        """
        Generates a data frame for the daily index realtime reports. Separate from main function for easy testing.
        """
        price_index_df = self.get_price_index_realtime()
        return_index_df = self.get_return_index_realtime()
        combined_index_df = self.transform_concat_price_return_dfs(price_df=price_index_df, return_df=return_index_df)
        combined_index_with_market_value = self.add_market_value_calculation_realtime(combined_index_df)
        combined_index_with_market_value = combined_index_with_market_value.iloc[::-1]
        combined_index_with_market_value = self.post_process_fidelity_dataframe(combined_index_with_market_value)
        return combined_index_with_market_value

    def get_price_index_realtime(self) -> pd.DataFrame:
        """
        This function retrieves price index data for realtime reports.
        """
        return self._get_index_realtime_with_pytz(self.price_index)

    def get_return_index_realtime(self) -> pd.DataFrame:
        """
        This function retrieves return index data for realtime reports.
        """
        return self._get_index_realtime_with_pytz(self.return_index)

    def _get_index_realtime_with_pytz(self, index_name: str) -> pd.DataFrame:
        """
        This function retrieves index data for realtime reports
        using the native datetime library and pytz library timezone as a parameter.
        :param index_name: Name of index to retrieve
        """
        return self._get_index_realtime(index_name=index_name,
                                        source_time_zone=pytz.timezone(self.timezone_name),
                                        target_time_zone=pytz.timezone("UTC"))

    def _get_index_realtime_with_zone_info(self, index_name: str) -> pd.DataFrame:
        """
        This function retrieves index data for realtime reports using pytz library
        using the native datetime library and ZoneInfo library timezone as a parameter.
        :param index_name: Name of index to retrieve
        """
        return self._get_index_realtime(index_name=index_name,
                                        source_time_zone=ZoneInfo(self.timezone_name),
                                        target_time_zone=ZoneInfo("UTC"))

    def _get_index_realtime(self, index_name: str, source_time_zone, target_time_zone):
        end_time_dt = datetime.datetime(year=self.reporting_date.year,
                                        month=self.reporting_date.month,
                                        day=self.reporting_date.day,
                                        hour=self.hour,
                                        tzinfo=source_time_zone).astimezone(target_time_zone)
        start_time_dt = end_time_dt - datetime.timedelta(days=1)

        start_time_dt_str = start_time_dt.strftime("%Y-%m-%dT%H0000")
        end_time_dt_str = end_time_dt.strftime("%Y-%m-%dT%H0000")

        def _internal_get_index_realtime() -> pd.DataFrame:
            return client.get_index_levels(indexes=[index_name],
                                           frequency="15s",
                                           start_time=start_time_dt_str,
                                           end_time=end_time_dt_str,
                                           page_size=10000,
                                           end_inclusive=True,
                                           start_inclusive=False,
                                           ).to_dataframe()

        return RetryUtil.retry_function(func=_internal_get_index_realtime, max_retries=5, retry_delay=2)

    def get_price_index_daily(self) -> pd.DataFrame:
        """
        This function gets price index data for daily reports.
        """
        return self._get_index_daily(self.price_index)

    def get_return_index_daily(self) -> pd.DataFrame:
        """
        This function gets return index data for daily reports.
        """
        return self._get_index_daily(self.return_index)

    def _get_index_daily(self, index_name: str) -> pd.DataFrame:
        def _internal_get_index_daily():
            return client.get_index_levels(indexes=[index_name],
                                           frequency=self.frequency,
                                           start_time=self.start_time_dt_str,
                                           end_time=self.end_time_dt_str,
                                           end_inclusive=True,
                                           page_size=10000).to_dataframe()

        return RetryUtil.retry_function(func=_internal_get_index_daily, max_retries=5, retry_delay=2)

    def add_market_value_calculation_realtime(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
        This function only needs to be implemented for realtime reports that actually user market_value as a field.
        Some do not (i.e. fidbtc and fideth), so by default this will do nothing.
        """
        return combined_df

    def add_market_value_calculation(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
        This function needs to be implemented by subclasses - there is different methodology for single and multi asset indices
        """
        raise NotImplementedError

    @staticmethod
    def transform_concat_price_return_dfs(price_df: pd.DataFrame, return_df: pd.DataFrame) -> pd.DataFrame:
        """
        Does some data transformations on dataframes from calling client.get_indexlevels(...).to_dataframe() in order to
        move the time -> index, rename the columns appropriately, and concat the dataframes
        :param price_df: dataframe with price index levels, like from FIDBTCP
        :param return_df: dataframe with return index levels, like from FIDBTCT
        :return: df with cols 'index_time', 'index_price', 'total_return'
        """
        return_df.rename({"level": "total_return", "time": "index_time"}, inplace=True, axis=1)
        return_df.drop(labels=["index"], axis=1, inplace=True)
        return_df.set_index('index_time', inplace=True)

        price_df.rename({"level": "index_price", "time": "index_time"}, inplace=True, axis=1)
        price_df.drop(labels=["index"], axis=1, inplace=True)
        price_df.set_index('index_time', inplace=True)

        joined_df = pd.concat([price_df, return_df], axis=1)
        joined_df = joined_df.iloc[::-1]
        joined_df.reset_index(inplace=True)

        return joined_df

    @staticmethod
    def post_process_fidelity_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """
        This function applies some small formatting transformations on the data, so that the format fully matches the
        file format that we have been uploading to the fidelity SFTP
        """
        df['index_time'] = pd.to_datetime(df['index_time']).dt.tz_convert(None)
        df = df.round(decimals=11)
        return df


class SingleAssetFidelityPair(AbstractFidelityIndexPairReportGenerator):
    """
    This class can be used to generate reports for the fidbtc and fideth files. Class methods added to make
    generating these reports even easier.
    """

    def __init__(self, price_index: str,
                 return_index: str,
                 underlying_asset: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 start_date: datetime.date = DAILY_REPORT_START_TIME,
                 frequency: str = "1d-ny-close",
                 hour: int = 16,
                 timezone_name: str = "America/New_York"):
        super().__init__(price_index=price_index,
                         return_index=return_index,
                         reporting_date=reporting_date,
                         start_date=start_date,
                         frequency=frequency,
                         hour=hour,
                         timezone_name=timezone_name)
        self.underlying_asset = underlying_asset

    def add_market_value_calculation(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
        For single asset indices we get the 'market_value' column by using the formula (weight for constituent in
        FIDBEIP) * index_price(FIDBEIP) = market_value
        """
        if self.underlying_asset != "btc" and self.underlying_asset != "eth":
            """
            We exclude Solana because we don't have an index with 2 assets for it.
            """
            return combined_df

        def _internal_get_fidbeip_constituents() -> pd.DataFrame:
            return client.get_index_constituents(indexes=["FIDBEIP"],
                                                 frequency=self.frequency,
                                                 start_time=self.start_time_dt_str,
                                                 end_time=self.end_time_dt_str,
                                                 end_inclusive=True,
                                                 page_size=10000).to_dataframe()

        constituent_weights = RetryUtil.retry_function(func=_internal_get_fidbeip_constituents, max_retries=5,
                                                       retry_delay=2)

        def get_weight_from_constituents(row: str, target: str = "btc") -> float:
            constituents = json.loads(row.replace("'", '"'))
            for item in constituents:
                if item['asset'] == target:
                    return float(item['weight'])

        constituent_weights['asset_weights'] = constituent_weights.apply(
            lambda row: get_weight_from_constituents(row['constituents'], self.underlying_asset), axis=1)

        constituent_weights = constituent_weights.iloc[::-1]
        constituent_weights.reset_index(inplace=True)

        fidbeip_prices = self._get_index_daily("FIDBEIP")
        fidbeip_prices = fidbeip_prices.iloc[::-1]
        fidbeip_prices.reset_index(inplace=True)

        combined_df['market_value'] = fidbeip_prices['level'] * constituent_weights['asset_weights']

        return combined_df

    @classmethod
    def create_fidbtc_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> SingleAssetFidelityPair:
        """
        Generates a FIDBTC reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        :param hour: hour
        """
        return SingleAssetFidelityPair(price_index="FIDBTCP", return_index="FIDBTCT",
                                       underlying_asset="btc", reporting_date=reporting_date, hour=hour)

    @classmethod
    def create_fidbtcl_reporter(cls, reporting_date: datetime.date.today()) -> SingleAssetFidelityPair:
        """
        Generates a FIDBTC reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        """
        return SingleAssetFidelityPair(price_index="FIDBTCP", return_index="FIDBTCT",
                                       underlying_asset="btc", reporting_date=reporting_date,
                                       start_date=reporting_date, frequency="15s",
                                       hour=16, timezone_name="Europe/London")

    @classmethod
    def create_fideth_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> SingleAssetFidelityPair:
        """
        Generates a FIDETH reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        """
        return SingleAssetFidelityPair(price_index="FIDETHP", return_index="FIDETHT",
                                       underlying_asset="eth", reporting_date=reporting_date, hour=hour)

    @classmethod
    def create_fidbcr_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> SingleAssetFidelityPair:
        """
        Generates a FIDBCR reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        """
        return SingleAssetFidelityPair(price_index="FIDBCRP", return_index="FIDBCRT",
                                       underlying_asset="btc", reporting_date=reporting_date, hour=hour)

    @classmethod
    def create_fiderr_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> SingleAssetFidelityPair:
        """
        Generates a FIDERR reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        """
        return SingleAssetFidelityPair(price_index="FIDERRP", return_index="FIDERRT",
                                       underlying_asset="eth", reporting_date=reporting_date, hour=hour)

    @classmethod
    def create_fidsol_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> SingleAssetFidelityPair:
        """
        Generates a FIDSOL reporter object to make it a bit easier to use
        :param reporting_date: date to use for report generator
        """
        return SingleAssetFidelityPair(price_index="FIDSOLP", return_index="FIDSOLT",
                                       underlying_asset="sol", reporting_date=reporting_date, hour=hour)


class MultiAssetFidelityPair(AbstractFidelityIndexPairReportGenerator):
    """
    Separate class to handle creating reports for fidbe and fidbei files corresponding to the Ethereum and BTC combined
    indices for fidelity
    """

    def __init__(self, price_index: str,
                 return_index: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 hour: int = 16):
        super().__init__(price_index=price_index, return_index=return_index, reporting_date=reporting_date, hour=hour)
        self.DATA_CAPTURE_DATE = FidelityDateResolver.get_previous_capture_date_or_none(report_date=reporting_date)
        self.splyff_cache = {"eth": {}, "btc": {}}

    def get_splyff_cache(self, asset: str, data_date: datetime.date) -> float:
        data_capture_date = FidelityDateResolver.get_previous_capture_date_or_none(data_date)
        if data_capture_date is None:
            return 0.0
        date_string = data_capture_date.strftime("%Y-%m-%d")
        if date_string in self.splyff_cache[asset]:
            return self.splyff_cache[asset][date_string]
        else:
            def _internal_get_splyff() -> str:
                return client.get_asset_metrics(assets=asset, metrics="SplyFF",
                                                start_time=data_capture_date,
                                                end_time=data_capture_date,
                                                frequency="1d"
                                                ).first_page()[0]['SplyFF']

            splyff_asset = RetryUtil.retry_function(func=_internal_get_splyff, max_retries=5, retry_delay=2)
            self.splyff_cache[asset][date_string] = float(splyff_asset)
            return self.splyff_cache[asset][date_string]

    def get_splyff_btc(self) -> float:
        # For now will just hardcode data_capture date
        return self._get_splyff("btc")

    def get_splyff_eth(self) -> float:
        # For now will just hardcode data_capture date
        return self._get_splyff("eth")

    def _get_splyff(self, asset: str):
        def _internal_get_splyff() -> str:
            return client.get_asset_metrics(assets=asset, metrics="SplyFF",
                                            start_time=self.DATA_CAPTURE_DATE, end_time=self.DATA_CAPTURE_DATE,
                                            frequency="1d"
                                            ).first_page()[0]['SplyFF']

        splyff_btc = RetryUtil.retry_function(func=_internal_get_splyff, max_retries=5, retry_delay=2)

        return float(splyff_btc)

    def add_market_value_calculation_realtime(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
         This uses the formula:
           supply outstanding BTC (aka SplyFF metric) at DATA CAPTURE DATE * market_cap(BTC) * weight(BTC) in index
           supply outstanding ETH (aka SplyFF metric) at DATA CAPTURE DATE * market_cap(ETH) * weight(ETH) in index
        """
        price_eth = self._get_index_realtime_with_zone_info("FIDETHP")
        price_btc = self._get_index_realtime_with_zone_info("FIDBTCP")

        def calc_market_cap(row, splyff: float) -> float:
            if row['time'].date() < self.DATA_CAPTURE_DATE or not splyff:
                return ""
            price = float(row['level'])
            market_cap = splyff * price
            return market_cap

        price_btc['market_cap'] = price_btc.apply(
            lambda row: calc_market_cap(row, self.get_splyff_cache(asset="btc", data_date=row['time'].date())), axis=1)
        price_eth['market_cap'] = price_eth.apply(
            lambda row: calc_market_cap(row, self.get_splyff_cache(asset="eth", data_date=row['time'].date())), axis=1)

        price_btc = price_btc.iloc[::-1]
        price_eth = price_eth.iloc[::-1]
        price_btc.reset_index(inplace=True)
        price_eth.reset_index(inplace=True)

        combined_df['market_value'] = price_btc['market_cap'] + price_eth['market_cap']

        return combined_df

    def add_market_value_calculation(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
        This uses the formula:
          supply outstanding BTC (aka SplyFF metric) at DATA CAPTURE DATE * market_cap(BTC) * weight(BTC) in index
          supply outstanding ETH (aka SplyFF metric) at DATA CAPTURE DATE * market_cap(ETH) * weight(ETH) in index
        """

        price_eth = self._get_index_daily("FIDETHP")
        price_btc = self._get_index_daily("FIDBTCP")

        def calc_market_cap(row, splyff: float) -> float:
            if row['time'].date() < self.DATA_CAPTURE_DATE or not splyff:
                return ""
            price = float(row['level'])
            market_cap = splyff * price
            return market_cap

        price_btc['market_cap'] = price_btc.apply(
            lambda row: calc_market_cap(row, self.get_splyff_cache(asset="btc", data_date=row['time'].date())), axis=1)
        price_eth['market_cap'] = price_eth.apply(
            lambda row: calc_market_cap(row, self.get_splyff_cache(asset="eth", data_date=row['time'].date())), axis=1)

        price_btc = price_btc.iloc[::-1]
        price_eth = price_eth.iloc[::-1]
        price_btc.reset_index(inplace=True)
        price_eth.reset_index(inplace=True)

        combined_df['market_value'] = price_btc['market_cap'] + price_eth['market_cap']

        return combined_df

    @classmethod
    def create_fidbei_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> MultiAssetFidelityPair:
        """
        Creates a reporter object for the FIDBEI report
        :param reporting_date: datetime for the report
        :param hour: hour
        """
        return MultiAssetFidelityPair(price_index="FIDBEIP", return_index="FIDBEIT",
                                      reporting_date=reporting_date, hour=hour)


class FidelityEvenIndexPair(MultiAssetFidelityPair):
    """
    Handles calculating daily reporting for equal weight reporting
    This is separate than other reports because for the equal weight indices we need to solve for an "equal weight splyff",
    basically what the supply would be if ETH and BTC had even market caps
    """

    def __init__(self,
                 price_index: str,
                 return_index: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 hour: int = 16):
        super().__init__(price_index, return_index, reporting_date, hour=hour)

    EQUAL_WEIGHT_SPLYFF_ETH = None
    EQUAL_WEIGHT_SPLYFF_BTC = None
    EQUAL_WEIGHT_SPLYFF_CACHE: Dict[str, Dict[str, float]] = {"eth": {}, "btc": {}}

    def get_equal_weight_splyff_cache(self, asset: str, data_date: datetime.date) -> float:
        """
        This function calculates and sets the "equal weighted supply" for btc and eth. This is explained by what the supply
        of ETH and Bitcoin would be if their market caps were equal.
        """
        data_capture_date: datetime.date = FidelityDateResolver.get_previous_capture_date_or_none(report_date=data_date)
        if data_capture_date is None:
            return 0.0
        date_string = data_capture_date.strftime("%Y-%m-%d")
        if date_string in self.EQUAL_WEIGHT_SPLYFF_CACHE[asset]:
            return self.EQUAL_WEIGHT_SPLYFF_CACHE[asset][date_string]
        else:
            splyff_eth_on_capture = self.get_splyff_cache(asset="eth", data_date=data_date)
            splyff_btc_on_capture = self.get_splyff_cache(asset="btc", data_date=data_date)
            fidbtcp_level_at_data_capture_date = self._get_index_level("FIDBTCP", data_capture_date)
            fidethp_level_at_data_capture_date = self._get_index_level("FIDETHP", data_capture_date)
            market_cap_btc = splyff_btc_on_capture * fidbtcp_level_at_data_capture_date
            market_cap_eth = splyff_eth_on_capture * fidethp_level_at_data_capture_date
            market_cap_summed_half = (market_cap_btc + market_cap_eth) * .5
            equal_weight_splyff_eth = market_cap_summed_half / fidethp_level_at_data_capture_date
            equal_weight_splyff_btc = market_cap_summed_half / fidbtcp_level_at_data_capture_date
            self.EQUAL_WEIGHT_SPLYFF_CACHE["eth"][date_string] = equal_weight_splyff_eth
            self.EQUAL_WEIGHT_SPLYFF_CACHE["btc"][date_string] = equal_weight_splyff_btc
            return self.EQUAL_WEIGHT_SPLYFF_CACHE[asset][date_string]

    def _get_index_level(self, index_name: str, data_capture_date: datetime.date):
        def _internal_get_index_level() -> float:
            return float(
                client.get_index_levels(indexes=[index_name], start_time=data_capture_date, end_time=data_capture_date,
                                        frequency="1d-ny-close").first_page()[0]['level'])

        return RetryUtil.retry_function(func=_internal_get_index_level, max_retries=5, retry_delay=2)

    def add_market_value_calculation_realtime(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        price_eth = self._get_index_realtime_with_zone_info("FIDETHP")
        price_btc = self._get_index_realtime_with_zone_info("FIDBTCP")

        def calc_market_cap(row, splyff: float) -> float:
            if row['time'].date() < self.DATA_CAPTURE_DATE or not splyff:
                return ""
            price = float(row['level'])
            market_cap = splyff * price
            return market_cap

        price_btc['market_cap'] = price_btc.apply(lambda row: calc_market_cap(row, self.get_equal_weight_splyff_cache(
            asset="btc", data_date=row['time'].date())), axis=1)
        price_eth['market_cap'] = price_eth.apply(lambda row: calc_market_cap(row, self.get_equal_weight_splyff_cache(
            asset="eth", data_date=row['time'].date())), axis=1)
        price_btc = price_btc.iloc[::-1]
        price_eth = price_eth.iloc[::-1]
        price_btc.reset_index(inplace=True)
        price_eth.reset_index(inplace=True)
        combined_df['market_value'] = price_btc['market_cap'] + price_eth['market_cap']

        return combined_df

    def add_market_value_calculation(self, combined_df: pd.DataFrame) -> pd.DataFrame:
        """
        This uses the formula:
         supply outstanding BTC Equal Weighted* at DATA CAPTURE DATE * market_cap(BTC) * weight(BTC) in index
         supply outstanding ETH Equal Weighted* at DATA CAPTURE DATE * market_cap(ETH) * weight(ETH) in index

        * The concept of equal weighted SplyFF is that it's the hypothetical supply and bitcoin and eth if they're market
        caps were equal.
        """
        price_eth = self._get_index_daily("FIDETHP")
        price_btc = self._get_index_daily("FIDBTCP")

        def calc_market_cap(row, splyff: float) -> float:
            if row['time'].date() < self.DATA_CAPTURE_DATE:
                return ""
            price = float(row['level'])
            market_cap = splyff * price
            return market_cap

        price_btc['market_cap'] = price_btc.apply(lambda row: calc_market_cap(row, self.get_equal_weight_splyff_cache(
            asset="btc", data_date=row['time'].date())), axis=1)
        price_eth['market_cap'] = price_eth.apply(lambda row: calc_market_cap(row, self.get_equal_weight_splyff_cache(
            asset="eth", data_date=row['time'].date())), axis=1)
        price_btc = price_btc.iloc[::-1]
        price_eth = price_eth.iloc[::-1]
        price_btc.reset_index(inplace=True)
        price_eth.reset_index(inplace=True)
        combined_df['market_value'] = price_btc['market_cap'] + price_eth['market_cap']
        return combined_df

    @classmethod
    def create_fidebe_reporter(cls, reporting_date: datetime.date.today(), hour: int = 16) -> FidelityEvenIndexPair:
        """
        Crates a reporter for fidebe
        :param reporting_date: date for the report
        :param hour
        """
        return FidelityEvenIndexPair(price_index="FIDEBEP", return_index="FIDEBET", reporting_date=reporting_date,
                                     hour=hour)


if __name__ == '__main__':
    timezone = pytz.timezone("America/New_York")

    report_date = datetime.date.today() if datetime.datetime.now(
        timezone).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)

    fidebe_reporter_generator = FidelityEvenIndexPair.create_fidebe_reporter(datetime.date(2025, 3, 27))
    fidebe_reporter_generator.generate_daily_index_report(folder_location=".", file_prefix="fidebe")
    fidebe_reporter_generator.generate_realtime_index_report(folder_location=".", file_prefix="fidebe")

    fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
    fidbei_report_generator.generate_daily_index_report(folder_location=".", file_prefix="fidbei")
    fidbei_report_generator.generate_realtime_index_report(folder_location=".", file_prefix="fidbei")

    fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
    fidbtc_report_generator.generate_daily_index_report(folder_location=".", file_prefix="fidbtc")
    fidbtc_report_generator.generate_realtime_index_report(folder_location=".", file_prefix="fidbtc")

    fidbtcl_report_generator = SingleAssetFidelityPair.create_fidbtcl_reporter(reporting_date=report_date)
    fidbtcl_report_generator.generate_daily_index_report(folder_location=".", file_prefix="fidbtcl")
    fidbtcl_report_generator.generate_realtime_index_report(folder_location=".", file_prefix="fidbtcl")

    fidsol_report_generator = SingleAssetFidelityPair.create_fidsol_reporter(reporting_date=report_date)
    fidsol_report_generator.generate_daily_index_report(folder_location=".", file_prefix="fidsol")
    fidsol_report_generator.generate_realtime_index_report(folder_location=".", file_prefix="fidsol")
