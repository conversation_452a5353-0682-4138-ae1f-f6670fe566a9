from datetime import datetime

import pandas as pd

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.env_util import is_local, is_staging
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("FidelityDateResolver")


class FidelityDateResolver:
    CAPTURE_DATE_SERIES = {
        'Third Friday': pd.to_datetime(['2022-03-18', '2022-06-17', '2022-09-16', '2022-12-16',
                                        '2023-03-17', '2023-06-16', '2023-09-15', '2023-12-15',
                                        '2024-03-15', '2024-06-21', '2024-09-20', '2024-12-20',
                                        '2025-03-21', '2025-06-20', '2025-09-19', '2025-12-19',
                                        '2026-03-20', '2026-06-22', '2026-09-18', '2026-12-18',
                                        '2027-03-19', '2027-06-21', '2027-09-17', '2027-12-17',
                                        '2028-03-17', '2028-06-16', '2028-09-15', '2028-12-15']),
        '10 Business Days Before': pd.to_datetime(['2022-03-03', '2022-06-02', '2022-08-31', '2022-12-01',
                                                   '2023-03-02', '2023-06-01', '2023-08-30', '2023-11-30',
                                                   '2024-02-29', '2024-06-05', '2024-09-05', '2024-12-05',
                                                   '2025-03-06', '2025-06-04', '2025-09-04', '2025-12-04',
                                                   '2026-03-05', '2026-06-04', '2026-09-02', '2026-12-03',
                                                   '2027-03-04', '2027-06-03', '2027-09-01', '2027-12-02',
                                                   '2028-03-02', '2028-06-01', '2028-08-30', '2028-11-30'])
    }

    CAPTURE_DATE_TABLE = pd.DataFrame(CAPTURE_DATE_SERIES)

    @classmethod
    def is_in_proforma_reporting_window(cls, report_date: datetime.date) -> bool:
        """
        This functions uses the dates we have stored in proforma dates to check that it is a valid proforma date
        """
        input_date = pd.to_datetime(report_date)
        for idx, row in FidelityDateResolver.CAPTURE_DATE_TABLE.iterrows():
            if row['10 Business Days Before'] < input_date < row['Third Friday']:
                return True
        return False

    @classmethod
    def get_previous_capture_date_or_none(cls, report_date: datetime.date) -> datetime.date:
        """
        Finds the most recent data capture date ('10 Business Days Before') that occurred before the given date.
        Returns None if no such date exists.
        """
        input_date = pd.to_datetime(report_date)
        previous_third_friday = FidelityDateResolver.CAPTURE_DATE_TABLE.iloc[0]['Third Friday']
        for idx, row in FidelityDateResolver.CAPTURE_DATE_TABLE.iloc[1:].iterrows():
            if previous_third_friday < input_date < row['Third Friday']:
                return FidelityDateResolver.CAPTURE_DATE_TABLE.iloc[idx - 1]['10 Business Days Before'].to_pydatetime().date()

        return None

    @classmethod
    def get_previous_capture_date_or_fail(cls, report_date: datetime.date) -> datetime.date:
        data_capture_date = cls.get_previous_capture_date_or_none(report_date=report_date)
        if data_capture_date is not None:
            return data_capture_date

        report_date_str = report_date.strftime('%Y-%m-%d')
        raise ValueError(f"The reporting date: {report_date_str} does not fall within a reporting window.")

    @classmethod
    def resolve_capture_date_or_fail(cls, report_date: datetime.date) -> datetime.date:
        """
        This function will check if a date falls within a proforma date window, if it does not will raise ValueError,
        proforma reports are only run during the proforma window.
        """
        input_date = pd.to_datetime(report_date)
        for idx, row in cls.CAPTURE_DATE_TABLE.iterrows():
            if row['10 Business Days Before'] <= input_date < row['Third Friday']:
                return row['10 Business Days Before'].to_pydatetime().date()

        if is_local() or is_staging():
            log.info(f'{report_date} does not fall within a proforma date window but returning always {report_date} since we are in LOCAL or STAGING')
            return report_date

        report_date_str = report_date.strftime('%Y-%m-%d')
        raise ValueError(f"The reporting date: {report_date_str} does not fall within a reporting window.")
