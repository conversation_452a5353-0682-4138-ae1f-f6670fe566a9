import datetime
import os
from zoneinfo import ZoneInfo

import numpy as np
import pandas as pd
from pandas import DataFrame

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

client = CoinMetricsClientFactory.get_client()


class FidelityClosePriceExporter:

    target_zone_info: ZoneInfo = ZoneInfo("UTC")

    def __init__(self, indexes: [str], file_prefix: str, folder_location: str, source_time_zone: ZoneInfo,
                 report_date: datetime.date = datetime.date.today()):
        self.indexes = indexes
        self.file_prefix = file_prefix
        self.folder_location = folder_location
        self.source_time_zone = source_time_zone
        self.report_date = report_date

    def generate_report(self):
        data_df: DataFrame = self._get_index_levels()

        data_df = self._transform_data_frame(data_df)

        return self._write_to_csv(data_df)

    def _get_index_levels(self) -> pd.DataFrame:
        time_dt = datetime.datetime(year=self.report_date.year,
                                    month=self.report_date.month,
                                    day=self.report_date.day,
                                    hour=16,
                                    tzinfo=self.source_time_zone).astimezone(self.target_zone_info)
        time_dt_str = time_dt.strftime("%Y-%m-%dT%H0000")

        def _internal_get_index_levels() -> pd.DataFrame:
            return client.get_index_levels(indexes=self.indexes,
                                           frequency="15s",
                                           start_time=time_dt_str,
                                           end_time=time_dt_str,
                                           page_size=50,
                                           end_inclusive=True,
                                           start_inclusive=True,
                                           ).to_dataframe()

        return RetryUtil.retry_function(func=_internal_get_index_levels, max_retries=5, retry_delay=2)

    def _transform_data_frame(self, data_df: DataFrame) -> DataFrame:
        data_df.rename(columns={"index": "BBG Ticker", "time": "Date", "level": "Price"}, inplace=True)
        data_df['Date'] = data_df['Date'].apply(lambda date: date.strftime('%Y%m%d'))
        # The columns High and Low can be left empty, but the column name should exist.
        data_df = data_df.assign(High=np.nan, Low=np.nan)

        return self._after_transform_data_frame(data_df)

    def _after_transform_data_frame(self, data_df: DataFrame) -> DataFrame:
        return data_df

    def _write_to_csv(self, data_df: DataFrame):
        file_name = f"{self.file_prefix}_{self.report_date.strftime('%Y%m%d')}.csv"
        full_file_path = os.path.join(self.folder_location, file_name)
        data_df.to_csv(full_file_path, index=False)

        return full_file_path


class FidelityNYClosePriceExporter(FidelityClosePriceExporter):

    indexes = [
        "FIDBCRP",
        "FIDBCRT",
        "FIDBEIP",
        "FIDBEIT",
        "FIDBTCP",
        "FIDBTCT",
        "FIDEBEP",
        "FIDEBET",
        "FIDETHP",
        "FIDETHT",
        "FIDERRP",
        "FIDERRT",
        "FIDSOLP",
        "FIDSOLT"
    ]

    source_time_zone = ZoneInfo("America/New_York")

    def __init__(self, folder_location: str, report_date: datetime.date = datetime.date.today()):
        super().__init__(indexes=self.indexes, file_prefix="coinmetrics_price", folder_location=folder_location,
                         source_time_zone=self.source_time_zone, report_date=report_date)


class FidelityLondonClosePriceExporter(FidelityClosePriceExporter):

    indexes = [
        "FIDBTCP",
    ]

    source_time_zone = ZoneInfo("Europe/London")

    def __init__(self, folder_location: str, report_date: datetime.date = datetime.date.today()):
        super().__init__(indexes=self.indexes, file_prefix="coinmetrics_price", folder_location=folder_location,
                         source_time_zone=self.source_time_zone, report_date=report_date)

    def _after_transform_data_frame(self, data_df: DataFrame) -> DataFrame:
        data_df['BBG Ticker'] = data_df['BBG Ticker'].apply(lambda ticker: f"{ticker}L")
        return super()._after_transform_data_frame(data_df)


if __name__ == '__main__':
    ny_file = FidelityNYClosePriceExporter(folder_location="C:\\temp\data-pusher\\ny",
                                           report_date=datetime.date(2025, 3, 18)).generate_report()
    print(ny_file)

    london_file = FidelityLondonClosePriceExporter(folder_location="C:\\temp\data-pusher\ldn",
                                                   report_date=datetime.date(2025, 3, 18)).generate_report()
    print(london_file)
