from __future__ import annotations

import datetime
import os
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any

import pandas as pd

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_date_resolver import FidelityDateResolver
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("FidelityConstituentReport")

client = CoinMetricsClientFactory.get_client()

@dataclass
class Configuration:
    id: int
    index_full_name: str
    underlying_price_index: str = None
    assets: list[str] = field(default_factory=list)

@dataclass
class AssetConfiguration:
    name: str
    """
    SplyFF is not yet supported for Solana so we have to use SplyEstNtv.
    """
    use_splyestntv: bool = False
    """
    Solana is presented in single-asset indexes only so it has the fixed weight in the report.
    """
    has_multi_asset_index: bool = False

class FidelityConstituentReportMultiAssetPair:
    """
    Meant to group indices and underlying assets in a way that makes sense for the constituents report. The constituents
    report groups a price multi asset index, return multi asset index, and an underlying asset (i.e. btc or eth) and
    uses them to do various calculations
    """
    ASSETS = {
        "btc": AssetConfiguration(name="Bitcoin",use_splyestntv=False,has_multi_asset_index=True),
        "eth": AssetConfiguration(name="Ethereum",use_splyestntv=False,has_multi_asset_index=True),
        "sol": AssetConfiguration(name="Solana",use_splyestntv=True,has_multi_asset_index=False),
    }

    FIDELITY_INDEXES = {
        "FIDEBEP": Configuration(id=1006, index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Price Index",
                                 assets=["btc", "eth"]),
        "FIDEBET": Configuration(id=1006,
                                 index_full_name="Fidelity Equal Weight Bitcoin and Ethereum Total Return Index",
                                 assets=["btc", "eth"]),
        "FIDBEIT": Configuration(id=1003, index_full_name="Fidelity Bitcoin and Ethereum Total Return Index",
                                 assets=["btc", "eth"]),
        "FIDBEIP": Configuration(id=1003, index_full_name="Fidelity Bitcoin and Ethereum Price Index",
                                 assets=["btc", "eth"]),
        "FIDBTCP": Configuration(id=1001, index_full_name="Fidelity Bitcoin Price Return Index",
                                 underlying_price_index="FIDBTCP", assets=["btc"]),
        "FIDBTCT": Configuration(id=1001, index_full_name="Fidelity Bitcoin Total Return Index",
                                 underlying_price_index="FIDBTCP", assets=["btc"]),
        "FIDETHP": Configuration(id=1002, index_full_name="Fidelity Ethereum Price Return Index",
                                 underlying_price_index="FIDETHP", assets=["eth"]),
        "FIDETHT": Configuration(id=1002, index_full_name="Fidelity Ethereum Total Return Index",
                                 underlying_price_index="FIDETHP", assets=["eth"]),
        "FIDBCRP": Configuration(id=1004, index_full_name="Fidelity Bitcoin Reference Rate PR",
                                 underlying_price_index="FIDBCRP", assets=["btc"]),
        "FIDBCRT": Configuration(id=1004, index_full_name="Fidelity Bitcoin Reference Rate TR",
                                 underlying_price_index="FIDBCRP", assets=["btc"]),
        "FIDERRP": Configuration(id=1005, index_full_name="Fidelity Ethereum Reference Rate PR",
                                 underlying_price_index="FIDERRP", assets=["eth"]),
        "FIDERRT": Configuration(id=1005, index_full_name="Fidelity Ethereum Reference Rate TR",
                                 underlying_price_index="FIDERRT", assets=["eth"]),
        "FIDSOLP": Configuration(id=1007, index_full_name="Fidelity Solana Reference Rate PR",
                                 underlying_price_index="FIDSOLP", assets=["sol"]),
        "FIDSOLT": Configuration(id=1007, index_full_name="Fidelity Solana Reference Rate TR",
                                 underlying_price_index="FIDSOLT", assets=["sol"])
    }

    def __init__(self, index: str,
                 index_full_name: str,
                 underlying_price_index: str,
                 underlying_asset: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 proforma_date: Optional[datetime.date] = None):
        self.index = index
        self.index_full_name = index_full_name
        self.underlying_price_index = underlying_price_index
        self.underlying_asset = underlying_asset
        self.reporting_date = reporting_date
        self.DATA_CAPTURE_DATE = FidelityDateResolver.get_previous_capture_date_or_fail(report_date=self.reporting_date)

    def generate_report_row(self) -> List[Any]:
        """
        Generates a series that has all the values expects in a row in the order:
        date index_id return_type close currency index_ticker index_name asset_ticker asset_price
        asset_return1d marketcap weight index_shares
        """
        date_str = self.reporting_date.strftime("%m/%d/%Y")
        index_id = self.FIDELITY_INDEXES[self.index].id
        return_type = "price" if self.index.endswith("P") else "total"
        close = "NYC"
        currency = "USD"
        asset_ticker = self.underlying_asset.upper()
        asset_name = self.ASSETS[self.underlying_asset].name
        asset_price = self.get_asset_price_report_item()
        asset_return_1d = self.get_asset_return1d()
        marketcap = self.get_marketcap()
        weight = self.get_underlying_asset_weight_in_index()
        index_shares = self.get_index_shares()
        unscaled_shares = self.get_unscaled_shares()
        return [date_str, index_id, return_type, close, currency, self.index, self.index_full_name, asset_ticker,
                asset_name,
                asset_price, asset_return_1d, marketcap, weight, index_shares, unscaled_shares]

    def get_asset_price_report_item(self):
        """
        This method exists so it can be overwritten by proforma report, which calculates a theoretical price.
        """
        return self.get_asset_price(price_date=self.reporting_date)

    def get_unscaled_shares(self) -> int | float:
        """
        Gets the unscaled shares for the index.
        For the single asset indexes ths is just the SplyFF of the underlying asset so BTC or ETH.
        For the mixed ones this is the equal weighted SplyFF. So a hypoethtical Supply Outstanding calculated between ETH and BTC
        """
        if len(self.FIDELITY_INDEXES.get(self.index).assets) == 1:
            return 1
        elif self.index.startswith("FIDBEI"):
            return self.get_splyff()
        elif self.index.startswith("FIDEBE"):
            splyff_btc = self.get_splyff(asset="btc")
            splyff_eth = self.get_splyff(asset="eth")
            eth_price_data_capture = self.get_asset_price(price_date=self.DATA_CAPTURE_DATE, index="FIDETHP")
            btc_price_data_capture = self.get_asset_price(price_date=self.DATA_CAPTURE_DATE, index="FIDBTCP")
            combined_mktcap = splyff_btc * btc_price_data_capture + splyff_eth * eth_price_data_capture
            if self.underlying_asset == "eth":
                equal_weight_splyff_eth = .5 * (combined_mktcap / eth_price_data_capture)
                return equal_weight_splyff_eth
            elif self.underlying_asset == "btc":
                equal_weight_splyff_btc = .5 * (combined_mktcap / btc_price_data_capture)
                return equal_weight_splyff_btc
            else:
                raise ValueError(f"Unsupported underlying asset: {self.underlying_asset}")
        else:
            raise ValueError(f"Unsupported underlying index: {self.index}")

    def get_index_shares(self) -> float:
        """
        Formula for index shares is weight of underlying asset in index * price of underlyinhg asset price index / index price
        """
        if len(self.FIDELITY_INDEXES.get(self.index).assets) == 1:
            return 1
        asset_price = self.get_asset_price()
        weight = self.get_underlying_asset_weight_in_index()
        actual_index_price = self.get_index_price_index_level()
        return weight * actual_index_price / asset_price

    def get_asset_price(self, price_date: Optional[datetime.date] = None, index: Optional[str] = None) -> float:
        """
        Querys the index price for 1d-ny-close
        :param price_date: date to get price for - defaults to self.reporting_date
        :param index: index to get price for - defaults to self.underlying_price_index
        :return: float index price for the date using 1d-ny-close
        """
        if price_date is None:
            price_date = self.reporting_date
        index_to_query = index if index is not None else self.underlying_price_index

        def _internal_get_index_levels() -> list[dict[str, Any]]:
            start_time = price_date - datetime.timedelta(days=10)
            return client.get_index_levels(indexes=index_to_query,
                                           start_time=start_time,
                                           end_time=price_date,
                                           frequency="1d-ny-close").first_page()

        underlying_asset_index_price = RetryUtil.retry_function(func=_internal_get_index_levels, max_retries=5,
                                                                retry_delay=2)
        actual_price_level = float(underlying_asset_index_price[-1]["level"])
        return actual_price_level

    def get_marketcap(self) -> float:
        """
        Formula for marketcap is Splyff * price of underlying price index
        """
        price_underlying_price_index = self.get_asset_price(price_date=self.reporting_date)
        splyff = self.get_splyff()
        marketcap = splyff * price_underlying_price_index
        return marketcap

    def get_asset_return1d(self) -> float:
        previous_day = self.reporting_date - datetime.timedelta(days=1)
        asset_price_previous_day = self.get_asset_price(price_date=previous_day)
        asset_price_reporting_date = self.get_asset_price(price_date=self.reporting_date)
        asset_return1d = asset_price_reporting_date / asset_price_previous_day - 1
        return asset_return1d

    def get_splyff(self, asset: Optional[str] = None) -> float:
        asset = self.underlying_asset if asset is None else asset
        if self.ASSETS[asset].use_splyestntv:
            metric = "SplyEstNtv"
        else:
            metric = "SplyFF"

        def _internal_get_asset_metrics() -> str:
            start_time = self.DATA_CAPTURE_DATE - datetime.timedelta(days=10)
            end_time = self.DATA_CAPTURE_DATE

            return client.get_asset_metrics(assets=asset,
                                            start_time=start_time,
                                            end_time=end_time,
                                            frequency="1d",
                                            metrics=metric,
                                            ).first_page()[-1][metric]

        splyff = RetryUtil.retry_function(func=_internal_get_asset_metrics, max_retries=5, retry_delay=2)

        return float(splyff)

    def get_index_price_index_level(self, **kwargs) -> float:
        """
        This function gets the index level for the actual index if its a price index i.e. FIDEBEP or FIDBEIP but if it
        is a total return index, like FIDBEIT, it will instead get the index level for the corresponding price index,
        in the case of FIDBEIT this would be FIDBEIP
        """
        index_param = self.index if self.index.endswith("P") else self.index.replace("T", "P")
        query_params = self.get_query_params(**kwargs)
        query_params.update({"indexes": index_param})
        query_params.update({"start_time": datetime.datetime.strptime(query_params["start_time"],
                                                                      "%Y-%m-%d").date() - datetime.timedelta(days=10)})

        def _internal_get_index_levels() -> float:
            return float(client.get_index_levels(**query_params).first_page()[-1]['level'])

        result = RetryUtil.retry_function(func=_internal_get_index_levels, max_retries=5, retry_delay=2)

        return result

    def get_underlying_asset_weight_in_index(self) -> float:
        if len(self.FIDELITY_INDEXES.get(self.index).assets) == 1:
            return 1
        query_params = self.get_query_params()

        def _internal_get_index_constituents() -> dict[str, Any]:
            return client.get_index_constituents(**query_params).first_page()[0]

        constituents_data_reporting_date = RetryUtil.retry_function(func=_internal_get_index_constituents,
                                                                    max_retries=5, retry_delay=2)
        weights_of_assets: List[Dict[str, str]] = constituents_data_reporting_date['constituents']
        for asset_weights_dict in weights_of_assets:
            if asset_weights_dict['asset'] == self.underlying_asset:
                return float(asset_weights_dict['weight'])
        raise ValueError(f"{self.underlying_asset} not found in constituents of index: {self.index} on"
                         f" {self.reporting_date.strftime('%Y-%m-%d')}")

    def get_query_params(self, **kwargs) -> Dict:
        query_params = {"indexes": self.index,
                        "start_time": self.reporting_date.strftime("%Y-%m-%d"),
                        "end_time": self.reporting_date.strftime("%Y-%m-%d"),
                        "frequency": "1d-ny-close"}
        for item, value in kwargs.items():
            query_params.update({item: value})
        return query_params

    @classmethod
    def create_btc_pairs(cls,
                         reporting_date: datetime.date = datetime.date.today(),
                         proforma_date: Optional[datetime.date] = None) -> List[
        FidelityConstituentReportMultiAssetPair]:
        """
        Generates BTC and ETH pairs for BTC for each Fidelity index, for a particular day,
         to avoid typing errors for the index/ asset names and ease of use
        """
        common_params = {
            "underlying_asset": "btc",
            "reporting_date": reporting_date,
            "proforma_date": proforma_date
        }
        result_index_pairs = []
        for index_ticker, index_conf in cls.FIDELITY_INDEXES.items():
            if "btc" not in index_conf.assets:
                continue
            index_pair = cls(index=index_ticker,
                             index_full_name=index_conf.index_full_name,
                             underlying_price_index=index_conf.underlying_price_index if index_conf.underlying_price_index is not None else "FIDBTCP",
                             **common_params)
            result_index_pairs.append(index_pair)
        return result_index_pairs

    @classmethod
    def create_eth_pairs(cls,
                         reporting_date: datetime.date.today(),
                         proforma_date: Optional[datetime.date] = None
                         ) -> List[FidelityConstituentReportMultiAssetPair]:
        """
        Generates BTC and ETH pairs for ETH for each Fidelity Index, for a particular day, to avoid typing errors
        for the index/ asset names and ease of use
        """
        common_params = {
            "underlying_asset": "eth",
            "reporting_date": reporting_date,
            "proforma_date": proforma_date
        }
        result_index_pairs = []
        for index_ticker, index_conf in cls.FIDELITY_INDEXES.items():
            if "eth" not in index_conf.assets:
                continue
            index_pair = cls(index=index_ticker,
                             index_full_name=index_conf.index_full_name,
                             underlying_price_index=index_conf.underlying_price_index if index_conf.underlying_price_index is not None else "FIDETHP",
                             **common_params)
            result_index_pairs.append(index_pair)
        return result_index_pairs

    @classmethod
    def create_sol_pairs(cls,
                         reporting_date: datetime.date.today(),
                         proforma_date: Optional[datetime.date] = None
                         ) -> List[FidelityConstituentReportMultiAssetPair]:
        """
        Generates classes for SOL for each Fidelity Index, for a particular day, to avoid typing errors
        for the index/ asset names and ease of use
        """
        common_params = {
            "underlying_asset": "sol",
            "reporting_date": reporting_date,
            "proforma_date": proforma_date
        }
        result_index_pairs = []
        for index_ticker, index_conf in cls.FIDELITY_INDEXES.items():
            if "sol" not in index_conf.assets:
                continue
            index_pair = cls(index=index_ticker,
                             index_full_name=index_conf.index_full_name,
                             underlying_price_index=index_conf.underlying_price_index if index_conf.underlying_price_index is not None else "FIDSOLP",
                             **common_params)
            result_index_pairs.append(index_pair)
        return result_index_pairs

class ProformaConstituentsReport(FidelityConstituentReportMultiAssetPair):
    """
    Separate class, used to calculate theoretical weights for Proforma report
    """

    def __init__(self,
                 index: str,
                 index_full_name: str,
                 underlying_price_index: str,
                 underlying_asset: str,
                 reporting_date: datetime.date = datetime.date.today(),
                 proforma_date: Optional[datetime.date] = None):
        super().__init__(index, index_full_name, underlying_price_index, underlying_asset, reporting_date,
                         proforma_date)
        self.DATA_CAPTURE_DATE = FidelityDateResolver.resolve_capture_date_or_fail(report_date=reporting_date)
        self.splyff_btc = self.get_splyff(asset="btc")
        self.splyff_eth = self.get_splyff(asset="eth")
        self.eth_price_data_capture = self.get_asset_price(price_date=self.DATA_CAPTURE_DATE, index="FIDETHP")
        self.btc_price_data_capture = self.get_asset_price(price_date=self.DATA_CAPTURE_DATE, index="FIDBTCP")
        self.combined_mktcap = self.splyff_btc * self.btc_price_data_capture + self.splyff_eth * self.eth_price_data_capture
        self.equal_weight_splyff_eth = .5 * (self.combined_mktcap / self.eth_price_data_capture)
        self.equal_weight_splyff_btc = .5 * (self.combined_mktcap / self.btc_price_data_capture)
        self.btc_price_report_date = self.get_asset_price(index="FIDBTCP")
        self.eth_price_report_date = self.get_asset_price(index="FIDETHP")
        self.combined_mktcap_equal_weight = self.equal_weight_splyff_eth * self.eth_price_report_date + self.equal_weight_splyff_btc * self.btc_price_report_date

    def get_index_price_index_level(self, **kwargs) -> float:
        """
        This method is overwritten in order to support the functionality for calculating theoretical prices instead
        of the actual price we serve during the time period. This was due to Fidelity request and methodology provided
        by them.

        The formula is OriginalPriceIndex * ((WeightEth * PriceIndexEth) + (WeightBtc * PriceIndexBtc) / (MarketCapEth * MarketCapBtc))
        """
        index = self.index if self.index.endswith("P") else self.index.replace("T", "P")
        index_price_at_data_capture_date = self.get_asset_price(index=index, price_date=self.DATA_CAPTURE_DATE)
        if self.index.startswith("FIDE"):
            result = index_price_at_data_capture_date * (
                        self.equal_weight_splyff_eth * self.eth_price_report_date + self.equal_weight_splyff_btc * self.btc_price_report_date) / self.combined_mktcap
        else:
            result = index_price_at_data_capture_date * (
                        self.splyff_eth * self.eth_price_report_date + self.splyff_btc * self.btc_price_report_date) / self.combined_mktcap

        return result

    def get_underlying_asset_weight_in_index(self, asset: Optional[str] = None) -> float:
        """
        This uses marketcap of asset i.e. for FIDBEIP is marketcap BTC / sum(marketcap btc + marketcap eth)
        """
        if not self.ASSETS[self.underlying_asset].has_multi_asset_index:
            return 1.0
        if self.index.startswith("FIDE"):
            report_asset = asset if asset else self.underlying_asset
            if report_asset == "eth":
                price_asset_report_date = self.get_asset_price(index="FIDETHP")
                mkt_cap_asset = self.equal_weight_splyff_eth * price_asset_report_date
                weight = mkt_cap_asset / self.combined_mktcap_equal_weight
            elif report_asset == "btc":
                price_asset_report_date = self.get_asset_price(index="FIDBTCP")
                mkt_cap_asset = self.equal_weight_splyff_btc * price_asset_report_date
                weight = mkt_cap_asset / self.combined_mktcap_equal_weight
            return weight
        else:
            splyff_btc = self.get_splyff(asset="btc")
            splyff_eth = self.get_splyff(asset="eth")
            eth_price = self.get_asset_price(index="FIDETHP")
            btc_price = self.get_asset_price(index="FIDBTCP")
            combined_mktcap = splyff_btc * btc_price + splyff_eth * eth_price
            marketcap = self.get_marketcap()
            weight = marketcap / combined_mktcap
            return weight


def generate_fidelity_constituents_file(folder_location: str,
                                        reporting_date: datetime.date = datetime.date.today(),
                                        proforma_date: Optional[datetime.date] = None) -> str:
    """
    This function generates the fidelity constituents file, which contains complex calculations for fidelity about hte
    multi asset indexes.
    :param folder_location: Location that the file should be created at
    :param reporting_date: date that should be used for reporting
    :proforma_date: data capture date for the report
    """
    data_df = generate_fidelity_constituents_report_df(reporting_date=reporting_date, proforma_date=proforma_date)
    file_base = "fidelity" if proforma_date is None else "proforma"
    full_file_path = os.path.join(folder_location,
                                  f"{file_base}-constituents-{reporting_date.strftime('%Y-%m-%d')}.csv")
    data_df.drop("index", axis=1, inplace=True)
    data_df.to_csv(full_file_path, index=False)
    return full_file_path


def generate_fidelity_constituents_report_df(reporting_date: datetime.date = datetime.date.today(),
                                             proforma_date: Optional[datetime.date] = None) -> pd.DataFrame:
    constituent_reporter = FidelityConstituentReportMultiAssetPair if proforma_date is None else ProformaConstituentsReport
    full_list_index_pairs = constituent_reporter.create_sol_pairs(reporting_date=reporting_date,
                                                                  proforma_date=proforma_date)
    full_list_index_pairs.extend(
        constituent_reporter.create_eth_pairs(reporting_date=reporting_date, proforma_date=proforma_date)
    )
    full_list_index_pairs.extend(
        constituent_reporter.create_btc_pairs(reporting_date=reporting_date, proforma_date=proforma_date)
    )
    df_cols = ["date", "index_id", "return_type", "close", "currency", "index_ticker", "index_name", "asset_ticker",
               "asset_name", "asset_price", "asset_return1d", "marketcap", "weight", "index_shares", "unscaled_shares"]
    df = pd.DataFrame(columns=df_cols)
    for index_pair in full_list_index_pairs:
        row = index_pair.generate_report_row()
        df.loc[len(df.index)] = row
    df.sort_values(by=["index_id", "index_ticker", "asset_ticker"], inplace=True, ascending=False)
    df.reset_index(inplace=True)
    return df


def get_report_date(timestamp: datetime.datetime,
                    hour_cutoff: int = 16) -> datetime.date:
    """
    This function returns what date the report should be run for based on when it is triggered. The use of this function
    is for the case that a report relies on data that becomes available at 4pm say. If it is run before 4pm
    America/New_York time then it should return the previous date else the current date
    :param timestamp: datetime to check, often will be time triggered
    :param hour_cutoff: cutoff in America/New_York hours for it to be one date or the next
    """
    report_date = timestamp.date() if timestamp.hour >= hour_cutoff else timestamp.date() - datetime.timedelta(days=1)
    return report_date


if __name__ == '__main__':
    test_file1 = generate_fidelity_constituents_file(folder_location=".", reporting_date=datetime.date(2025, 3, 20))
    print("test_file1:", test_file1)
    test_file2 = generate_fidelity_constituents_file(folder_location=".", reporting_date=datetime.date(2025, 3, 20), proforma_date=datetime.date(2025, 3, 20))
    print("test_file2:", test_file2)

    # Generate all dates for the year 2025
    start_date = datetime.date(2025, 1, 1)
    end_date = datetime.date(2025, 12, 31)

    date_objects = [start_date + datetime.timedelta(days=i) for i in range((end_date - start_date).days + 1)]

    # Iterate through each date and call the method
    for date_obj in date_objects:
        result = FidelityDateResolver.is_in_proforma_reporting_window(date_obj)
        if result:
            print(f"{date_obj}")
            # generate_fidelity_constituents_file(".", date_obj)
            # generate_fidelity_constituents_file(".", date_obj, date_obj)
            # break
