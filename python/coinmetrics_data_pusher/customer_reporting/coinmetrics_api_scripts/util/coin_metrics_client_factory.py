import os

from coinmetrics.api_client import CoinMetricsClient

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("CoinMetricsClientFactory")


class CoinMetricsClientFactory:

    @staticmethod
    def get_client() -> CoinMetricsClient:
        client = CoinMetricsClient(api_key=os.environ.get('CM_API_KEY'))
        api_base_url = os.environ.get('CM_API_BASE_URL')
        log.info(f"api_base_url: {api_base_url}")
        if api_base_url is not None:
            client._api_base_url = api_base_url

        return client
