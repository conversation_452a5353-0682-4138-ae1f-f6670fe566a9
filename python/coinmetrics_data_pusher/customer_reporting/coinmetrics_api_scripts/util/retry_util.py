import time

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("RetryUtil")


class RetryUtil:

    @staticmethod
    def retry_function(func, max_retries=3, retry_delay=1):
        last_exception = None
        for attempt in range(max_retries):
            try:
                result = func()
                # If successful, return the result
                return result
            except Exception as e:
                last_exception = e
                log.error(msg=f"Error calling {func.__name__} at the attempt # {attempt + 1}", exc_info=e)
                # Wait for a while before retrying
                time.sleep(retry_delay)
        if last_exception is not None:
            # Re-raise the last exception
            raise last_exception
