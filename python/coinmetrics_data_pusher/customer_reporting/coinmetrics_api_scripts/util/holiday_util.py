from datetime import *

from dateutil.relativedelta import *


def is_july_3_a_weekday(input_date: datetime) -> bool:
    """
    Day before Independence Day
    """
    return input_date.month == 7 and input_date.day == 3 and 0 <= input_date.weekday() <= 4


def is_july_4_a_weekday(input_date: datetime) -> bool:
    """
    Independence Day
    """
    return input_date.month == 7 and input_date.day == 4 and 0 <= input_date.weekday() <= 4


def is_an_after_thanksgiving_day(input_date: datetime) -> bool:
    """
    Day after Thanksgiving Day
    """
    thanksgiving_date = datetime.date(datetime(input_date.year, 11, 1) + relativedelta(weekday=TH(+4)))
    after_date = thanksgiving_date + timedelta(days=1)

    return input_date.year == after_date.year and input_date.month == after_date.month and input_date.day == after_date.day


def is_december_24_a_weekday(input_date: datetime) -> bool:
    """
    Day before Christmas Day
    """
    return input_date.month == 12 and input_date.day == 24 and 0 <= input_date.weekday() <= 4


def is_martin_luther_king_jr_day(input_date: datetime) -> bool:
    """
    <PERSON> Jr. Day is a federal holiday in the United States observed on the third Monday of January each year.
    """
    holiday_date = datetime.date(datetime(input_date.year, 1, 1) + relativedelta(weekday=MO(+3)))

    return input_date.year == holiday_date.year and input_date.month == holiday_date.month and input_date.day == holiday_date.day


def is_jimmy_carter_honor_a_weekday(input_date: datetime) -> bool:
    """
    Jimmy Carter Honor Day
    US stock markets to remain closed in honor of Jimmy Carter on National Day of Mourning
    """
    return input_date.year == 2025 and input_date.month == 1 and input_date.day == 9 and 0 <= input_date.weekday() <= 4


def is_memorial_day(input_date: datetime | date) -> bool:
    """
    Memorial Day is a federal holiday in the United States observed on the last Monday of May.
    """
    if input_date.month != 5:
        return False

    # Find the last Monday in May
    memorial_day = datetime.date(datetime(input_date.year, 5, 1) + relativedelta(day=31, weekday=MO(-1)))

    # Convert input_date to date if it's a datetime object
    input_date_as_date = input_date.date() if isinstance(input_date, datetime) else input_date

    return input_date_as_date == memorial_day


def is_early_close_day(input_date: datetime) -> bool:
    return is_july_3_a_weekday(input_date) or is_july_4_a_weekday(input_date) or is_an_after_thanksgiving_day(
        input_date) or is_december_24_a_weekday(
        input_date) or is_martin_luther_king_jr_day(input_date) or is_jimmy_carter_honor_a_weekday(
        input_date) or is_memorial_day(input_date)


if __name__ == '__main__':
    # Generate all dates for the year 2025
    start_date = date(2024, 1, 1)
    end_date = date(2026, 12, 31)

    date_objects = [start_date + timedelta(days=i) for i in range((end_date - start_date).days + 1)]

    # Iterate through each date and call the method
    for date_obj in date_objects:
        result = is_early_close_day(date_obj)
        if result:
            print(f"{date_obj}")
