import logging
from logging import Logger


class LoggerFactory:

    @staticmethod
    def get_logger(name: str = "celery-worker-logger") -> Logger:
        logger: Logger = logging.getLogger(name)
        formatter = logging.Formatter('[%(asctime)s] [%(processName)s] [%(levelname)s] [%(name)s] - %(message)s',
                                      datefmt='%d/%b/%Y %H:%M:%S')
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)
        logger.setLevel(logging.INFO)

        return logger
