import os
from datetime import datetime, timedelta
from typing import List, Tuple

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

client = CoinMetricsClientFactory.get_client()


def generate_end_of_month_daily_rr_file(folder_location: str = "../reporting") -> str:
    """
    Generates a file with EOM reference rates at market close (4pm EST) for the last day of the month, for all assets
    that suport reference rates
    :return: str name of the file generated
    """
    start, end = get_start_and_end_time_to_query()
    assets = RetryUtil.retry_function(func=get_assets_with_reference_rates, max_retries=10, retry_delay=10)
    timezone = "America/New_York"
    file = generate_reference_rates_file(assets_to_query=assets, start=start, end=end, folder_location=folder_location, timezone=timezone)
    return file


def generate_reference_rates_file(assets_to_query: List[str], start: datetime, end: datetime, folder_location: str, timezone: str) -> str:
    """
    Creates a file that has reference rates for each day in the month, ending each day on NY market close, 4pm EST
    :param assets_to_query: list of string of assets i.e. ['btc', 'eth', 'ada']
    :param start: datetime start date for query inclusive
    :param end: datetime end date for query inclusive
    :param folder_location: str location on machine where the files should be stored
    :param timezone: timezone to use, defaults to UTC
    :return: str name of the file generated
    """
    reference_rates = client.get_asset_metrics(assets_to_query, 'ReferenceRate', frequency='1d-16:00', start_time=start,
                                               end_time=end, page_size=100, timezone=timezone)

    lines = [f"{rate['time']},{rate['asset']},{rate['ReferenceRate']}"
             for rate in reference_rates]

    result_file_name = f'reference_rates_{start.strftime("%B").lower()}_{start.year}.csv'
    full_file_path = os.path.join(folder_location, result_file_name)
    with open(full_file_path, 'w') as reference_rates_file:
        reference_rates_file.write('rate_time,name,rate_final_price\n')
        reference_rates_file.write('\n'.join(sorted(lines)))
    return full_file_path


def get_assets_with_reference_rates() -> List[str]:
    """
    Helper function to get all CM assets with reference rates for 1d
    :return: List of str of assets with reference rates
    """
    metrics = client.catalog_metrics('ReferenceRate')
    for frequency_info in metrics[0]['frequencies']:
        if frequency_info['frequency'] == '1d':
            return frequency_info['assets']


def get_start_and_end_time_to_query() -> Tuple[datetime, datetime]:
    """
    :return: Returns a tuple of the last day of the month and the first day of the month
    """
    today = datetime.now()
    end = datetime(today.year, today.month, 1)
    start = end - timedelta(days=1)
    return start, end

if __name__ == '__main__':
    start = datetime.now()
    generate_end_of_month_daily_rr_file()
    end = datetime.now()
    print(f"Time taken to run: {end - start}")