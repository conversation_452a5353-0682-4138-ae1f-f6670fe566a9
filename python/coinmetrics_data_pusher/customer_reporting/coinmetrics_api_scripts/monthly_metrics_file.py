from datetime import timed<PERSON><PERSON>, date
from typing import Tuple

from pandas import DataFrame

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("MonthlyMetricsFile")

client = CoinMetricsClientFactory.get_client()


class MonthlyMetricsFile:
    assets = [
        "btc",
        "cbbtc_base.eth",
        "dai.e_base.eth",
        "eth",
        "sol",
        "xrp",
        "usdc",
        "usdc.e_op.eth",
        "usdc_base.eth",
        "usdc_eth",
        "usdc_op.eth",
        "usdc_sol",
        "usdc_trx",
        "usdt.e_op.eth",
        "usdt_eth",
        "usdt_omni",
        "usdt_sol",
        "usdt_trx",
        "pyusd_eth",
        "fdusd_eth",
    ]

    metrics = [
        "TxTfrValNtv"
    ]

    def __init__(self):
        self.start, self.end = self.get_previous_month_dates()

    def generate_monthly_metrics_file(self, folder_location: str) -> str:
        asset_metrics = RetryUtil.retry_function(func=self._internal_get_asset_metrics, max_retries=10, retry_delay=5)
        start_str = self.start.strftime("%Y_%m_%d")
        end_str = self.end.strftime("%Y_%m_%d")
        file_name = f"{folder_location}/monthly_metrics_{start_str}_{end_str}.csv"
        asset_metrics.to_csv(file_name, index=False)
        return file_name

    def generate_email_header(self) -> str:
        start_str = self.start.strftime("%Y-%m-%d")
        end_str = self.end.strftime("%Y-%m-%d")
        return f"Monthly Metrics File for {start_str} - {end_str}"

    def generate_email_body(self) -> str:
        return self.generate_email_header()

    def _internal_get_asset_metrics(self) -> DataFrame:
        log.info(f"requesting assets between {self.start} - {self.end}")
        return client.get_asset_metrics(assets=self.assets,
                                        metrics=self.metrics,
                                        frequency='1d',
                                        start_time=self.start,
                                        start_inclusive=True,
                                        end_time=self.end,
                                        end_inclusive=True,
                                        paging_from='start',
                                        page_size=10000).to_dataframe()

    def get_previous_month_dates(self) -> Tuple[date, date]:
        # Get today's date
        today = date.today()

        # Calculate the first day of the current month
        first_day_this_month = today.replace(day=1)

        # Calculate the last day of the previous month
        last_day_previous_month = first_day_this_month - timedelta(days=1)

        # Calculate the first day of the previous month
        first_day_previous_month = last_day_previous_month.replace(day=1)

        # Return the dates as a tuple
        return first_day_previous_month, last_day_previous_month


if __name__ == '__main__':
    reporter = MonthlyMetricsFile()
    reporter.generate_monthly_metrics_file(folder_location='C:\\temp\\data-pusher')
    print(reporter.generate_email_header())
    print(reporter.generate_email_body())
