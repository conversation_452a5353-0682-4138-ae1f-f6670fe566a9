import datetime
import os
from typing import <PERSON>ple

import psycopg2
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.external_price_readers import \
    FidelityNowPriceReader
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("FidelityNYCloseCompareReporter")

client = CoinMetricsClientFactory.get_client()


class FidelityNYCloseCompareReporter:

    FIDBTCP_ID = 1001

    def __init__(self, report_date: datetime.date):
        self.report_date: datetime.date = report_date
        self.report_date_4pm_ny_as_str: str = self._get_4pm_ny_as_str()
        self.location: str = os.environ.get('LOCATION', 'unknown')
        self.environment: str = os.environ.get('ENVIRONMENT', 'unknown')
        self.report_body: str = ""

    def get_4pm_ny_report_header(self) -> str:
        ny_tz = pytz.timezone("America/New_York")
        dt: datetime.date = ny_tz.localize(datetime.datetime(year=self.report_date.year,
                                                             month=self.report_date.month,
                                                             day=self.report_date.day,
                                                             hour=16))

        formatted_dt = dt.strftime('%Y-%m-%dT%H:%M:%S%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]

        return formatted_dt

    def generate_full_report(self) -> str:
        self._generate_report_body()
        report_header: str = f"Fidelity BTC Index Comparison Report for {self.get_4pm_ny_report_header()}[America/New_York] in {self.location}/{self.environment}"
        report_footer: str = f"Report Generated at UTC Time: {datetime.datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}."
        full_report = report_header + "\n\n" + self.report_body + "\n\n" + report_footer
        return full_report

    def _generate_report_body(self):
        self.report_body = ""
        original_price, original_trades_number = self._get_index_price_and_trades(self.FIDBTCP_ID)
        log.debug(f"original price {original_price} and original number of trades {original_trades_number} at {self.report_date_4pm_ny_as_str}")

        self.report_body += f"Location/Environment: {self.location}/{self.environment}."
        self.report_body += f"\n\nOriginal Index Value: ${original_price}, calculated from {original_trades_number} trades."

        now_price, now_trades_number = self._get_now_price()

        log.debug(f"now price {original_price} and now number of trades {original_trades_number} at {self.report_date_4pm_ny_as_str}")

        self.report_body += f"\n\nFinal (Delayed) Index Value: ${now_price}, calculated from {now_trades_number} trades."

        if now_price == 0.0:
            self.report_body += f"\n\nThere was an issue with getting the delayed price. Delayed price: {now_price}, delayed trades: {now_trades_number}."
            self._add_mentions()
            return

        price_has_difference: bool = now_price != 0.0 and original_price != now_price
        price_diff, price_diff_percentage = self._calculate_float_diff(original_value=original_price, now_value=now_price)
        self.report_body += f"\n\nPrice Difference: ${price_diff} ({price_diff_percentage})."

        trades_diff, trades_diff_percentage = self._calculate_int_diff(original_value=original_trades_number, now_value=now_trades_number)
        self.report_body += f"\n\nTrades Difference: {trades_diff} ({trades_diff_percentage})."

        """
        Levels unchanged, trades unchanged -> no alert
        Levels unchanged, trades changed -> no alert
        Levels changed, trades unchanged -> alert
        Levels changed, trades changed -> alert
        """
        if price_has_difference:
            self._add_mentions()

    def _add_mentions(self):
        self.report_body += f"\n\nMentions:"
        self.report_body += f"\n- DQE <!subteam^S02G3DXMP5Z>"
        self.report_body += f"\n- Michael <@UJGTZT0M9>, Evgeny <@U02NR3MTGQ5>, Maksim <@U02PJPQL9JM>, Alex B <@UJ2PMUC9F>"
        self.report_body += f"\n- Dorian <@U03J315D2P5>, Brandon <@U01EM7WAE4T>"

    def _calculate_float_diff(self, original_value: float, now_value: float) -> Tuple[str, str]:
        diff = now_value - original_value
        diff_percentage = diff / original_value * 100
        return f"{diff:.6f}", f"{diff_percentage:.3f}%"

    def _calculate_int_diff(self, original_value: int, now_value: int) -> Tuple[str, str]:
        diff = now_value - original_value
        diff_percentage = diff / original_value * 100
        return f"{diff}", f"{diff_percentage:.3f}%"

    def _get_index_price_and_trades(self, index_id: int) -> Tuple[float, int]:
        def _internal_get_index_price_and_trades() -> Tuple[float, int]:
            """
            This function queries the price and trades for the fidelity index.
            :return: Tuple[price, number_trades] if it passes else Tuple[0.0, 0]
            """
            unix_timestamp_est = self._get_4pm_ny_unix_timestamp()
            cursor = self.get_db_connection().cursor()
            database_schema = "staging" if os.environ.get("ENVIRONMENT") == "LOCAL" else os.environ.get("ENVIRONMENT")
            query = f"""
            select 
              index_price,
              index_num_trades 
            from 
              {database_schema}.fidelity_index
            where 
              index_id = {index_id}
            and 
              index_time = timestamp 'epoch' + {unix_timestamp_est} * interval '1 second'
            """
            print(query)
            cursor.execute(query)
            data = cursor.fetchall()
            if data:
                price = float(data[0][0])
                trades_number = int(data[0][1])
                return price, trades_number
            else:
                return 0.0, 0
        try:
            return RetryUtil.retry_function(_internal_get_index_price_and_trades)
        except Exception as e:
            log.error(msg=f"Error when querying index price and trades for {index_id} at {self.report_date_4pm_ny_as_str}", exc_info=e)
            return 0.0, 0

    def _get_now_price(self) -> Tuple[float, int]:
        reader = FidelityNowPriceReader()
        unix_timestamp_est = self._get_4pm_ny_unix_timestamp()

        def _internal_get_now_price() -> Tuple[float, int]:
            price, trades_number = reader.get_price(at_sec=unix_timestamp_est)
            return price, trades_number

        try:
            return RetryUtil.retry_function(func=_internal_get_now_price, max_retries=10, retry_delay=3)
        except Exception as e:
            log.error(msg=f"Error when requesting now price and trades for {unix_timestamp_est} at {self.report_date_4pm_ny_as_str}", exc_info=e)
            return 0.0, 0

    def _get_4pm_ny_unix_timestamp(self) -> int:
        """
        Gets the NY close time as a unix timestamp.
        """
        ny_tz = pytz.timezone("America/New_York")
        report_date_for_unix = ny_tz.localize(
            datetime.datetime(year=self.report_date.year, month=self.report_date.month, day=self.report_date.day, hour=16)
        ).astimezone(tz=pytz.UTC)

        return int(report_date_for_unix.replace(tzinfo=pytz.UTC).timestamp())

    @staticmethod
    def get_db_connection():
        index_db = os.environ.get("PG_INDEX_DB")
        conn = psycopg2.connect(host=os.environ.get('PGHOST'),
                                database=index_db,
                                user=os.environ.get('PGUSER'),
                                password=os.environ.get('PGPASSWORD'))
        return conn

    def _get_4pm_ny_as_str(self) -> str:
        """
        Gets the NY close time as a string.
        """
        ny_tz = pytz.timezone("America/New_York")
        dt = ny_tz.localize(datetime.datetime(year=self.report_date.year,
                                              month=self.report_date.month,
                                              day=self.report_date.day,
                                              hour=16))
        return dt.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')


if __name__ == '__main__':
    full_report_result = FidelityNYCloseCompareReporter(report_date=datetime.date(year=2023, month=11, day=2)).generate_full_report()
    print(f"full report:\n{full_report_result}")
