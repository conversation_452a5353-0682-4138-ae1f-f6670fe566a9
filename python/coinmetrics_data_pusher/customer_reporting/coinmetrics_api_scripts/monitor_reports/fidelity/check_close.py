import datetime
from dataclasses import dataclass
from typing import List

import pytz
from pandas import DataFrame
from requests.exceptions import HTTPError

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.external_price_readers import \
    CoinMarketCapPrice<PERSON>ead<PERSON>, CoinGeckoPriceReader, ExternalPrice, Asset<PERSON>uff<PERSON><PERSON><PERSON><PERSON>
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("IndexReporter")

client = CoinMetricsClientFactory.get_client()


@dataclass
class IndexLevels:
    index: str
    low_price: float
    high_price: float

@dataclass
class IndexConfiguration:
    index: str
    id: int
    underlying_asset: str
    total_return_multiplier: float

class IndexReporter:
    """
    Class to generate reports for fidelity index close reports. These reports look like:
    Please see below for a copy of the Closing Levels check for the Fidelity Index.
    Please feel free to reach out with any questions.
    Thank you.

    Fidelity FIDBTC Index report for America/New_York on 2023-04-18T16:00:00-04:00

    Price available within 1 min - Price Index : PASSED [ 30224.67 ]
    Price available within 1 min - TR Index : PASSED [ 33869.3139476769 ]

    Intraday Low and High - Price Index : PASSED [ 29172.1/30454.11 ]
    Intraday Low and High - TR Index : PASSED [ 32689.819720547/34126.4209861377 ]

    Asset Sufficiency Check (# trades) - Price Index : PASSED [ 1961 ]

    External Sources Check - Price Index > Coin Market Cap : PASSED [ 30230.939657548563 ]
    External Sources Check - Price Index > CoinGecko : PASSED [ 30190.0 ]
    External Sources Check - Price Index > CM RTRR : PASSED [ 30235.99 ]

    All Checks : PASSED [ true ]
    """

    indexConfigurations = [
        IndexConfiguration(index="FIDBTC", id=1001, underlying_asset="btc", total_return_multiplier=1.12058507),
        IndexConfiguration(index="FIDETH", id=1002, underlying_asset="eth", total_return_multiplier=1.10523543),
        IndexConfiguration(index="FIDBCR", id=1004, underlying_asset="btc", total_return_multiplier=1.12039317),
        IndexConfiguration(index="FIDERR", id=1005, underlying_asset="eth", total_return_multiplier=1.10406166),
        IndexConfiguration(index="FIDSOL", id=1007, underlying_asset="sol", total_return_multiplier=1.0),
    ]

    indexToConfs: dict[str, IndexConfiguration] = {config.index: config for config in indexConfigurations}

    def __init__(self,
                 index: str,
                 report_date: datetime.date,
                 hour: int,
                 timezone: str = "America/New_York",
                 index_email_name: str = None):
        if index not in self.indexToConfs:
            raise ValueError(f"Was expecting the index {index} to be one of: {self.indexToConfs.keys()}")
        self.index = index
        self.index_email_name = index if index_email_name is None else index_email_name
        self.underlying_asset = self.indexToConfs[self.index].underlying_asset
        self.timezone = timezone

        utc_tz = pytz.timezone("UTC")
        utc_close_time = utc_tz.localize(datetime.datetime(year=report_date.year,
                                                           month=report_date.month,
                                                           day=report_date.day,
                                                           hour=hour))

        ny_tz = pytz.timezone(self.timezone)
        self.report_date_close_time = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                                       month=report_date.month,
                                                                       day=report_date.day,
                                                                       hour=hour)).astimezone(pytz.timezone("UTC"))

        hours_diff = utc_close_time.hour - self.report_date_close_time.hour
        self.diff = f"{hours_diff:+03d}:00"

        self.report_date_query_param = self.report_date_close_time.strftime('%Y-%m-%dT%H:%M:%S.%f')

        self.price_index = self.index + "P"
        self.total_return_index = self.index + "T"
        self.asset_sufficiency_checker = AssetSufficiencyChecker(index_id=self.indexToConfs[self.index].id,
                                                                 report_date=report_date,
                                                                 hour=hour,
                                                                 timezone=self.timezone)


    def create_close_report(self):
        result_lines = []
        report_header = """
All,
Please see below for a copy of the Closing Levels check for the Fidelity Index.
Please feel free to reach out with any questions.
Thank you.
        """
        timestamp_line = f"Fidelity {self.index_email_name} Index report for {self.timezone} on {self.report_date_close_time.strftime('%Y-%m-%dT%H:%M:%S%z')}{self.diff}\n"
        result_lines.append(report_header)
        result_lines.append(timestamp_line)
        try:
            price_index_price = self.get_price_index_price()
            price_available_within_1_min = "PASSED"
        except IndexError:
            price_available_within_1_min = "FAILED"
            price_index_price = 0

        try:
            return_index_price = self.get_total_return_price()
            return_available_within_1_min = "PASSED"
        except IndexError:
            return_available_within_1_min = "FAILED"
            return_index_price = 0

        price_index_line = f"Price available within 1 min - Price Index : {price_available_within_1_min} [ {price_index_price} ] "
        return_index_line = f"Price available within 1 min - TR Index : {return_available_within_1_min} [ {return_index_price} ] \n"
        result_lines.append(price_index_line)
        result_lines.append(return_index_line)
        try:
            intraday_low_high_price = self.get_intraday_low_high_price_index()
            intraday_passed_price = "PASSED" if intraday_low_high_price.low_price and intraday_low_high_price.high_price else "FAILED"
            asset_sufficiency = self.asset_sufficiency_checker.perform_asset_sufficiency_check()
            asset_suffiency_check = "PASSED" if asset_sufficiency[0] else "FAILED"
            asset_sufficiency_trades = asset_sufficiency[1]
        except IndexError as e:
            intraday_low_high_price = IndexLevels(index=self.price_index, high_price=0.0, low_price=0.0)
            intraday_passed_price = "FAILED"
            asset_suffiency_check = "FAILED"
            asset_sufficiency_trades = 0


        intraday_price_line = f"Intraday Low and High - Price Index : {intraday_passed_price} [ {intraday_low_high_price.low_price}/{intraday_low_high_price.high_price} ]"

        result_lines.append(intraday_price_line)

        try:
            intraday_low_high_return = self.get_intraday_low_high_total_return_index()
            rate_low = intraday_low_high_return.low_price
            rate_high = intraday_low_high_return.high_price
            intraday_passed_return = "PASSED" if rate_low != rate_high and rate_low > 0.0 and rate_high > 0.0 else "FAILED"
        except IndexError as e:
            intraday_low_high_return = IndexLevels(index=self.price_index, high_price=0.0, low_price=0.0)
            intraday_passed_return = "FAILED"

        intraday_return_line = f"Intraday Low and High - TR Index : {intraday_passed_return} [ {intraday_low_high_return.low_price}/{intraday_low_high_return.high_price} ]\n"
        result_lines.append(intraday_return_line)
        asset_sufficiency_line = f"Asset Sufficiency Check (# trades) - Price Index: {asset_suffiency_check}: [ {asset_sufficiency_trades} ]\n"
        result_lines.append(asset_sufficiency_line)
        external_price_check_lines = self.create_external_sources_lines(price_index_price)
        result_lines.extend(external_price_check_lines)
        result_str = "\n".join(result_lines)
        return result_str

    def create_external_sources_lines(self, price_index_price: float) -> List[str]:
        lines = []
        for label, func in {
            "Coin Market Cap": lambda: self.get_coinmarket_cap_price(),
            "CoinGecko": lambda: self.get_coingecko_price(),
            "CM RTRR": lambda: self.get_cm_rtrr(),
        }.items():
            pass_result: str = "FAILED"
            external_price_value: float = 0.0
            try:
                external_price: ExternalPrice = func()
                external_price_value = external_price.price
                if self.passed_external_source_check(price_index_price, external_price_value):
                    pass_result = "PASSED"
            except (IndexError, HTTPError) as e:
                log.error(msg=f"External check failed for {label} with an error. Error:\n{e}")
            lines.append(f"External Sources Check - Price Index > {label} : {pass_result} [ {external_price_value} ]")

        return lines

    @staticmethod
    def passed_external_source_check(index_close_price: float, external_source_price: float) -> bool:
        if external_source_price is None:
            return False
        else:
            return abs(1.0 - external_source_price / index_close_price) < 0.025

    def get_cm_rtrr(self, frequency: str = "1s") -> ExternalPrice:
        data = client.get_asset_metrics(assets=self.underlying_asset, metrics="ReferenceRateUSD",
                                        start_time=self.report_date_query_param,
                                        end_time=self.report_date_query_param, frequency=frequency).first_page()
        reference_rate = data[-1]["ReferenceRateUSD"]

        return ExternalPrice(
            price=float(reference_rate),
            name=self.underlying_asset,
            time=self.report_date_close_time,
        )

    def get_total_return_price(self) -> float:
        """
        Gets the total return price for the given index
        """
        index_level = client.get_index_levels(indexes=self.total_return_index, start_time=self.report_date_query_param,
                                       end_time=self.report_date_query_param, frequency="15s").first_page()[-1]['level']
        return index_level

    def get_price_index_price(self) -> float:
        """
        Gets the total return price for the given index
        """
        index_level = client.get_index_levels(indexes=self.price_index, start_time=self.report_date_query_param,
                                       end_time=self.report_date_query_param, frequency="15s").first_page()[-1]['level']
        return float(index_level)

    def get_coingecko_price(self) -> ExternalPrice:
        reader = CoinGeckoPriceReader()
        price = reader.get_price(asset=self.underlying_asset)
        return price

    def get_coinmarket_cap_price(self) -> ExternalPrice:
        reader = CoinMarketCapPriceReader()
        price = reader.get_price(asset=self.underlying_asset)
        return price

    def get_intraday_low_high_price_index(self) -> IndexLevels:
        return self._get_intraday_low_high_index(index=self.price_index, multiplier=1)

    def get_intraday_low_high_total_return_index(self) -> IndexLevels:
        return self._get_intraday_low_high_index(index=self.total_return_index, multiplier=self.indexToConfs[self.index].total_return_multiplier)

    def _get_intraday_low_high_index(self, index: str, multiplier: float) -> IndexLevels:
        previous_day = self.convert_dt_to_cm_query_param(self.report_date_close_time - datetime.timedelta(days=1))
        report_day = self.convert_dt_to_cm_query_param(self.report_date_close_time)

        def _internal_get_intraday_low_high_index() -> DataFrame:
            return client.get_index_candles(indexes=index, start_time=previous_day,
                                            end_time=report_day, frequency="1h",
                                            page_size=100).to_dataframe()

        candles = RetryUtil.retry_function(func=_internal_get_intraday_low_high_index, max_retries=3, retry_delay=1)
        if "price_low" in candles and "price_high" in candles:
            min_level = candles['price_low'].min() * multiplier
            max_level = candles['price_high'].max() * multiplier
            return IndexLevels(index=index, low_price=min_level, high_price=max_level)
        else:
            log.warning(msg=f"No index candles for {index} between {previous_day} and {report_day}: {candles}")
            return IndexLevels(index=index, low_price=0.0, high_price=0.0)

    @staticmethod
    def convert_dt_to_cm_query_param(dt: datetime.datetime) -> str:
        date_string = dt.strftime('%Y-%m-%dT%H:%M:%S.%f')
        return date_string


if __name__ == '__main__':
    eth_report = IndexReporter(index="FIDETH", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16)
    print(eth_report.create_close_report())

    btc_report = IndexReporter(index="FIDBTC", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16)
    print("------------------------------")
    print(btc_report.create_close_report())

    btcl_report = IndexReporter(index="FIDBTC", index_email_name="FIDBTCL", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16, timezone="Europe/London")
    print("------------------------------")
    print(btcl_report.create_close_report())

    bcr_report = IndexReporter(index="FIDBCR", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16)
    print("------------------------------")
    print(bcr_report.create_close_report())

    err_report = IndexReporter(index="FIDERR", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16)
    print("------------------------------")
    print(err_report.create_close_report())

    sol_report = IndexReporter(index="FIDSOL", report_date=datetime.date.today() - datetime.timedelta(days=1), hour=16)
    print("------------------------------")
    print(sol_report.create_close_report())
