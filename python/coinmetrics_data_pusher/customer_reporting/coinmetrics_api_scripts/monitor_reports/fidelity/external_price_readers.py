import datetime
import pytz
import requests
import psycopg2
import os
from dataclasses import dataclass
from abc import abstractmethod, <PERSON>
from typing import Tuple, Dict

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory


@dataclass
class ExternalPrice:
    """Data class to store external price information."""
    name: str
    time: datetime.datetime
    price: float


class ExternalPriceReader(ABC):

    @abstractmethod
    def get_price(self, asset: str) -> ExternalPrice:
        """
        Gets price of an asset for an outside data source,
         must be implemented by subclasses
        """
        raise NotImplementedError


class CoinMarketCapPriceReader(ExternalPriceReader):
    """
    Class to read prices from Coin Market Cap for btc, eth, and sol
    """

    ASSET_TO_ID_MAP: Dict[str, str] = {
        "btc": "1",
        "eth": "1027",
        "sol": "5426"
    }

    def get_price(self, asset: str) -> ExternalPrice:
        asset_lower = asset.lower()
        if asset_lower not in self.ASSET_TO_ID_MAP:
            raise ValueError(f"Asset {asset_lower} must be one of: {', '.join(self.ASSET_TO_ID_MAP.keys())}")

        asset_id = self.ASSET_TO_ID_MAP[asset_lower]

        key = "049ababf-316d-4da8-ad3e-638985a404e7"
        url = f"https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest?id={asset_id}"

        headers = {
            "X-CMC_PRO_API_KEY": key,
            "Accept": "application/json",
        }

        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            response.raise_for_status()
        data = response.json()
        price = data['data'][asset_id]['quote']['USD']['price']
        timestamp = datetime.datetime.strptime(
            data['status']['timestamp'],
            '%Y-%m-%dT%H:%M:%S.%fZ'
        ).replace(tzinfo=datetime.timezone.utc)
        external_price = ExternalPrice(
            price=price,
            name=asset,
            time=timestamp
        )
        return external_price


class CoinGeckoPriceReader(ExternalPriceReader):
    """
    Class to read prices from CoinGecko
    """
    ASSET_TO_NAME_MAP: Dict[str, str] = {
        "btc": "bitcoin",
        "eth": "ethereum",
        "sol": "solana",
    }

    log = LoggerFactory.get_logger("CoinGeckoPriceReader")

    def get_price(self, asset: str) -> ExternalPrice:
        asset_lower = asset.lower()

        if asset_lower not in self.ASSET_TO_NAME_MAP:
            raise ValueError(f"Asset {asset_lower} must be one of: {', '.join(self.ASSET_TO_NAME_MAP.keys())}")

        asset_name = self.ASSET_TO_NAME_MAP[asset_lower]

        coin_gecko_url = f"https://api.coingecko.com/api/v3/simple/price?ids={asset_name}&vs_currencies=usd&include_last_updated_at=true"
        response = requests.get(coin_gecko_url)
        if response.status_code != 200:
            response.raise_for_status()
        data = response.json()
        price = data[asset_name]['usd']

        # Check if 'last_updated_at' is in the response, otherwise use current time
        if 'last_updated_at' in data[asset_name]:
            timestamp = datetime.datetime.fromtimestamp(
                data[asset_name]['last_updated_at'],
                tz=datetime.timezone.utc
            )
            self.log.info(f"Using timestamp from CoinGecko response for {asset_name}: {timestamp}.")
        else:
            # Fallback to current time if 'last_updated_at' is not available
            timestamp = datetime.datetime.now(tz=datetime.timezone.utc)
            self.log.warning(f"'last_updated_at' not found in CoinGecko response for {asset_name}. Using current time: {timestamp}.")

        external_price = ExternalPrice(
            price=price,
            name=asset,
            time=timestamp
        )
        return external_price


class FidelityNowPriceReader:
    def get_price(self, at_sec: int) -> Tuple[float, int]:
        if os.environ.get("ENVIRONMENT") == "LOCAL":
            # Use port-forwarding:
            # kubectl port-forward -n candle-maker-11160973 service/market-data-factory-fidelity-index-1 8888:8080
            fidelity_index_url = f"http://localhost:8888/calculateAt/{at_sec}"
        else:
            fidelity_index_url = f"{os.environ.get('FIDELITY_INDEX_URL', 'http://market-data-factory-fidelity-index.candle-maker-11160973.svc:8080')}/calculateAt/{at_sec}"

        response = requests.get(fidelity_index_url)

        if response.status_code != 200:
            response.raise_for_status()

        data = response.json()
        number_of_trades: int = int(data['size'])
        index_price: float = float(data['index_price'])

        return index_price, number_of_trades

class AssetSufficiencyChecker:

    def __init__(self, index_id: int, report_date: datetime.date, hour: int, timezone: str):
        self.index_id = index_id
        target_tz = pytz.timezone(timezone)
        self.report_date = target_tz.localize(
            datetime.datetime(year=report_date.year, month=report_date.month, day=report_date.day, hour=hour)
        ).astimezone(tz=pytz.UTC)

    def perform_asset_sufficiency_check(self) -> Tuple[bool, int]:
        """
        This function performs the "Asset Sufficiency Check" in the FIDBTC and FIDETH monitor reports. This checks that
        there was trades corresponding to the prices created for these indexes. The reason this queries the database
        instead of the API, is because the original monitor report queries information from the database, and what we serve
        in the api for num trades calculated is a different measure.
        :return: Tuple[True, number_trades] if it passes else Tuple[False, 0]
        """
        timestamp_est = self.get_unix_timestamp()
        cursor = self.get_db_connection().cursor()
        database_schema = "staging" if os.environ.get("ENVIRONMENT") == "LOCAL" else os.environ.get("ENVIRONMENT")
        query = f"""
select date_part('epoch', index_time)::int8 index_time,
index_num_trades from {database_schema}.fidelity_index
where index_id = {self.index_id} and index_time = timestamp 'epoch' + {timestamp_est} * interval '1 second'
"""
        cursor.execute(query)
        data = cursor.fetchall()
        if data:
            num_trades = data[0][1]
            return True, num_trades
        else:
            return False, 0

    def get_unix_timestamp(self) -> int:
        return int(self.report_date.replace(tzinfo=pytz.UTC).timestamp())


    @staticmethod
    def get_db_connection():
        index_db = os.environ.get("PG_INDEX_DB")
        conn = psycopg2.connect(host=os.environ.get('PGHOST'),
                                database=index_db,
                                user=os.environ.get('PGUSER'),
                                password=os.environ.get('PGPASSWORD'))
        return conn


if __name__ == '__main__':
    fidetha = AssetSufficiencyChecker(index_id=1002,
                                      report_date=datetime.date(year=2023, month=6, day=1),
                                      hour=16,
                                      timezone="America/New_York")
    check_result = fidetha.perform_asset_sufficiency_check()
    print(check_result)

    fidbtcl = AssetSufficiencyChecker(index_id=1001,
                                      report_date=datetime.date(year=2024, month=5, day=22),
                                      hour=16,
                                      timezone="Europe/London")
    fidbtcl_check_result = fidbtcl.perform_asset_sufficiency_check()
    print(fidbtcl_check_result)

    coin_market_cap_price_reader = CoinMarketCapPriceReader()
    coin_market_cap_price = coin_market_cap_price_reader.get_price("sOL")
    print(f"coin_market_cap_price: {coin_market_cap_price}")

    coin_gecko_price_reader = CoinGeckoPriceReader()
    coin_gecko_price = coin_gecko_price_reader.get_price("sOL")
    print(f"coin_gecko_price: {coin_gecko_price}")
