import io
import json
import requests
import urllib.parse
import os
from typing import <PERSON><PERSON><PERSON><PERSON>

def get_from_environ_or_raise_error(key: str) -> str:
    """
    This method raises an error if the key can't be found in the environ. This is done this way because using
    os.environ['key'] rather than os.environ.get('key') it causes issues with the nix and django build.
    :param key: name of str
    :return str: of value from python
    """
    value = os.environ.get(key)
    if value is None:
        raise EnvironmentError(f"Env var: {key} is not defined and it needs to, raising error")
    return value


def upload_to_google_drive(folder_id: str, name: str, data: str) -> str:
    """
    Upload a file to Google Drive in the specified folder with the given name.

    :param folder_id: The ID of the Google Drive folder to upload the file to.
    :param name: The name of the file to be uploaded.
    :param data: The file content as a bytes-like object (io.BytesIO).
    :return: The ID of the uploaded file.

    This function retrieves the client_id, client_secret, and refresh_token
    from the environment variables, obtains an access token using the
    AccessTokenRequest and get_access_token functions, and uploads the file
    to Google Drive using the Google Drive API.

    Raises an exception if the file upload fails.
    """
    client_id = get_from_environ_or_raise_error("GOOGLE_CLIENT_ID")
    secret_id = get_from_environ_or_raise_error("GOOGLE_CLIENT_SECRET")
    refresh_token = get_from_environ_or_raise_error("GOOGLE_REFRESH_TOKEN")

    access_token_request= AccessTokenRequest(client_id=client_id, client_secret=secret_id, refresh_token=refresh_token)
    access_token = get_access_token(access_token_request)
    url = "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true"

    metadata = json.dumps({"name": name, "parents": [folder_id]})
    headers = {"Authorization": f"Bearer {access_token}"}

    files = {
        "metadata": ("metadata", metadata, "application/json; charset=UTF-8"),
        "file": (name, data, "application/octet-stream"),
    }

    response = requests.post(url, headers=headers, files=files)

    if response.status_code == 200:
        file_id = response.json()["id"]
        return file_id
    else:
        raise Exception(f"Failed to upload file: {response.text}")


class AccessTokenRequest(NamedTuple):
    client_id: str
    client_secret: str
    refresh_token: str


def get_access_token(params: AccessTokenRequest) -> str:
    """
    Obtain an access token using the provided AccessTokenRequest.

    :param params: An AccessTokenRequest object containing the client_id, client_secret, and refresh_token.
    :return: The access token as a string.

    This function sends a request to the Google OAuth2 token endpoint to exchange
    the refresh token for a new access token. Raises an exception if the request fails.
    """
    url = "https://www.googleapis.com/oauth2/v4/token"
    encoded = AccessTokenRequest(
        client_id=urllib.parse.quote(params.client_id),
        client_secret=urllib.parse.quote(params.client_secret),
        refresh_token=urllib.parse.quote(params.refresh_token),
    )
    body = f"client_id={encoded.client_id}&client_secret={encoded.client_secret}&grant_type=refresh_token&refresh_token={encoded.refresh_token}"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    response = requests.post(url, headers=headers, data=body)

    if response.status_code == 200:
        res = response.json()
        return res["access_token"]
    else:
        raise Exception(f"Failed to get access token: {response.text}")