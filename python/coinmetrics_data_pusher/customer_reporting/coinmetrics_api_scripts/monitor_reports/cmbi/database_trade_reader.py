import datetime
import os
from dataclasses import dataclass

import psycopg2

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("DatabaseTradeReader")

@dataclass
class Trade:
    time: datetime.datetime
    price: float


class DatabaseTradeReader:

    def __init__(self, report_date: datetime.date):
        self.report_date = report_date

    @staticmethod
    def get_db_connection():
        database = os.environ.get("PG_TRADES_DB")
        conn = psycopg2.connect(host=os.environ.get('PGHOST'),
                                database=database,
                                user=os.environ.get('PGUSER'),
                                password=os.environ.get('PGPASSWORD'))
        return conn

    def read_close_trade(self, exchange: str, base: str, quote: str) -> float:
        exchanges_id_dict = {
            "Binance.US": 35,
            "Binance": 4,
            "Bitfinex": 2,
            "Bittrex": 33,
            "Coinbase": 1,
            "FTX": 38,
            "Kraken": 6,
        }
        exchange_id = exchanges_id_dict[exchange]
        assets = {
            "BTC": 0,
            "ETH": 6,
            "ADA": 143,
            "ALGO": 1231,
            "BAT": 28,
            "DOT": 1321,
            "MATIC": 1208,
            "SOL": 1313,
            "AVAX": 1412,
            "CELO": 1322,
            "BNB": 98,
            "XTZ": 338,
            "KSM": 1288,
            "HNT": 1571,
            "USD": 3,
            "USDT": 100,
            "BONK": 3298,
            "TON": 1810
        }
        asset_id = assets[base]
        quote_id = assets[quote]
        sql = f"""
            select t.trade_time as time, t.trade_price as price
            from public.trades_spot_{exchange_id} t where t.trade_symbol = (
                select s.market_symbol
                from spot_metadata s 
                where s.market_exchange_id = {exchange_id}
                and s.market_base_id = {asset_id} 
                and s.market_quote_id = {quote_id}
                and s.market_is_current = true
                order by market_database_time desc
                limit 1
            )
            and t.trade_time <= timezone('America/New_York', '{self.report_date}'::date + '16h'::interval)
            order by t.trade_time desc limit 1
        """

        try:
            db_connection = DatabaseTradeReader.get_db_connection()
            cursor = db_connection.cursor()
            cursor.execute(sql)
            trade_data = cursor.fetchone()
            trade = Trade(*trade_data)
            return trade.price
        except Exception as e:
            log.error(f"{self.__class__.__name__} got exception while retrieving a close trade for {exchange}-{base}-{quote} using sql {sql}", e)
            return 0.0

if __name__ == '__main__':
    report_date = datetime.date.today() - datetime.timedelta(days=1)
    val = DatabaseTradeReader(report_date).read_close_trade("Coinbase", "BTC", 3)
    print(val)