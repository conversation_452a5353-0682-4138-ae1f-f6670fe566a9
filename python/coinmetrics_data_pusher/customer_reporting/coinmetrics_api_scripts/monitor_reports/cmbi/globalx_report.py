import datetime
from typing import List

import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()


class GlobalXReporter:
    """
    Class to facilitate writing CMBI reports. The globalx reports are in the format:

    All,
    Please see below for a copy of today's closing levels.
    Please feel free to reach out with any questions.
    Thank you.

    CMBI Index report for: 2023-05-12T16:00:00+01:00

    CMBI Bitcoin (CMBIBTC) close price is: 26424.6363842782
    CMBI Bitcoin (CMBIBTC) real-time price: 26333.0
    CMBI Ethereum (CMBIETH) close price is: 1772.34980742256
    CMBI Ethereum (CMBIETH) real-time price is: 1767.35
    CMBI Uniswap (CMBIUNI) close price is: 5.1063300993571
    CMBI Uniswap (CMBIUNI) real-time price: 5.076
    CMBI Chainlink (CMBILINK) close price is: 6.43721613442431
    CMBI Chainlink (CMBILINK) real-time price is: 6.412
    CMBI Aave (CMBIAAVE) close-time price is: 61.9002439742841
    CMBI Aave (CMBIAAVE) real-time price is: 61.64
    """
    REPORT_ASSSETS = ["CMBIBTC", "CMBIETH", "CMBIUNI", "CMBILINK", "CMBIAAVE"]
    def __init__(self, assets_to_report_on: List[str], report_date: datetime.date = datetime.date.today()):
        self.assets = assets_to_report_on
        self.report_date = report_date

    def get_4pm_london_report_header(self) -> str:
        london_tz = pytz.timezone("Europe/London")
        dt = london_tz.localize(datetime.datetime(year=self.report_date.year,
                                                              month=self.report_date.month,
                                                              day=self.report_date.day, hour=16))
        formatted_dt = dt.strftime('%Y-%m-%dT%H:%M:%S%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]

        return formatted_dt

    def get_4pm_london_query_param(self) -> str:
        """
        Gets the london close price for all assets
        """
        london_tz = pytz.timezone("Europe/London")
        four_pm_london = london_tz.localize(datetime.datetime(year=self.report_date.year,
                                                              month=self.report_date.month,
                                                              day=self.report_date.day, hour=16))
        four_pm_london_utc = four_pm_london.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
        return four_pm_london_utc

    def get_close_price_assets(self) -> pd.DataFrame:
        four_pm_london_utc = self.get_4pm_london_query_param()
        close_prices = client.get_index_levels(indexes=self.assets, start_time=four_pm_london_utc, end_time=four_pm_london_utc, frequency="1h").to_dataframe()
        close_prices.rename(columns={"level":"close_price"}, inplace=True)
        return close_prices

    def get_realtime_price_assets(self) -> pd.DataFrame:
        four_pm_london_utc = self.get_4pm_london_query_param()
        close_prices = client.get_index_levels(indexes=self.assets, start_time=four_pm_london_utc, end_time=four_pm_london_utc, frequency="1s").to_dataframe()
        close_prices.rename(columns={"level":"realtime_price"}, inplace=True)
        return close_prices

    def get_realtime_closed_combined_df(self) -> pd.DataFrame:
        close_price = self.get_close_price_assets()
        realtime = self.get_realtime_price_assets()
        reltime_close = close_price.merge(realtime, on="index", how="left")
        catalog_info = client.catalog_indexes(indexes=self.assets).to_dataframe()
        catalog_info = catalog_info[catalog_info['frequency'] == "1h"]
        catalog_info_just_indexes_name = catalog_info[['index', 'full_name']]
        combined = reltime_close.merge(catalog_info_just_indexes_name, on="index")
        # ordering the df as the assets are pasted in
        combined['index'] = pd.Categorical(combined['index'], self.assets)
        combined = combined.sort_values('index')
        combined = combined.reset_index(drop=True)
        return combined

    def generate_report_body(self):
        data = self.get_realtime_closed_combined_df()
        result_str = ""
        for row in data.index:
            new_lines = f"{data['full_name'][row]} ({data['index'][row]}) close price is: {data['close_price'][row]}\n" \
                        f"{data['full_name'][row]} ({data['index'][row]}) realtime price is: {data['realtime_price'][row]}\n"
            result_str += new_lines
        return result_str

    def generate_full_report(self) -> str:
        report_header = f"""
All,
Please see below for a copy of today's closing levels.
Please feel free to reach out with any questions.
Thank you.

CMBI Index report for: {self.get_4pm_london_report_header()}
        
"""
        report_body = self.generate_report_body()
        report_footer = "\nPlease feel free to reach <NAME_EMAIL> with any questions."
        full_report = report_header + report_body + report_footer
        return full_report



