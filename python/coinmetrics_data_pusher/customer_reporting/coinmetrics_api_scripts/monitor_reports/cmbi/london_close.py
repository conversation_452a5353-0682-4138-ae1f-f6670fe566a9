import datetime
from typing import List

import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()


class CMBILondonCloseReporter:

    REPORT_ASSETS = ["CMBIBTC", "CMBIETH", "CMBISOL", "CMBIAPE"]


    def __init__(self, assets: List[str], report_date: datetime.date = datetime.date.today()):
        self.assets = assets
        self.report_date = report_date

    def get_4pm_london_report_header(self) -> str:
        london_tz = pytz.timezone("Europe/London")
        dt = london_tz.localize(datetime.datetime(year=self.report_date.year,
                                                              month=self.report_date.month,
                                                              day=self.report_date.day, hour=16))
        formatted_dt = dt.strftime('%Y-%m-%dT%H:%M:%S%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]

        return formatted_dt

    def get_4pm_london_query_param(self) -> str:
        """
        Gets the london close price for all assets
        """
        london_tz = pytz.timezone("Europe/London")
        four_pm_london = london_tz.localize(datetime.datetime(year=self.report_date.year,
                                                              month=self.report_date.month,
                                                              day=self.report_date.day, hour=16))
        four_pm_london_utc = four_pm_london.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
        return four_pm_london_utc

    def generate_report_body(self) -> str:
        london_4pm = self.get_4pm_london_query_param()
        index_data = client.get_index_levels(indexes=self.assets, start_time=london_4pm, end_time=london_4pm, frequency="1h").to_dataframe()
        index_data['index'] = pd.Categorical(index_data['index'], self.assets)
        index_data.sort_values('index', inplace=True)
        index_data.reset_index(drop=True, inplace=True)
        result_str = ""
        for item in index_data.index:
            new_line = f"{index_data['index'][item]}:[{index_data['level'][item]}]\n"
            result_str += new_line
        return result_str

    def generate_full_report(self) -> str:
        report_header = f"""
All,
Please see below for a copy of the LDN Closing Levels for CMBI Indices.
Please feel free to reach out with any questions.
Thank you.

Index report for: {self.get_4pm_london_report_header()}[Europe/London]

"""
        report_body = self.generate_report_body()
        report_footer = "\nPlease feel free to reach <NAME_EMAIL> with any questions."
        full_report = report_header + report_body + report_footer
        return full_report

if __name__ == '__main__':
    full_report_result = CMBILondonCloseReporter(assets=CMBILondonCloseReporter.REPORT_ASSETS, report_date=datetime.date(year=2024, month=1, day=4)).generate_full_report()
    print(f"full report:\n{full_report_result}")