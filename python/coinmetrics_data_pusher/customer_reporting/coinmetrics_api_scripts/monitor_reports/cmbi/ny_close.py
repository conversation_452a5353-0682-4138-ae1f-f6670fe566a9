import datetime
import textwrap
from typing import Callable

import pytz
from pandas import DataFrame

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.external_price_readers import \
    CoinMarketCapPriceReader, CoinGeckoPriceReader
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("CMBINYCloseReporter")

client = CoinMetricsClientFactory.get_client()


class CMBINYCloseReporter:
    BTC_INDEXES = ["CMBIBTC", "CMBIBTCT"]

    ETH_INDEXES = ["CMBIETH", "CMBIETHT"]

    SECONDARY_INDEXES = ["CMBI10", "C<PERSON><PERSON>10<PERSON>", "<PERSON>MBI10<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CMBIAP<PERSON>"]

    ALL_12PM_UTC_INDEXES = ["CMBI10M"]

    NETWORK_INDEXES = ["CMBIHASH", "CMBIWORK"]

    ALL_16PM_NY_INDEXES = BTC_INDEXES + ETH_INDEXES + SECONDARY_INDEXES + NETWORK_INDEXES

    def __init__(self, report_date: datetime.date):
        self.report_date: datetime.date = report_date
        self.report_date_query_param_16pm_ny: str = self._get_4pm_ny_query_param()
        self.report_date_query_param_12pm_utc: str = self._get_12pm_utc_query_param()
        self.is_passed: bool = False
        self.report_body: str = ""
        self.function_to_names = {
            self._get_cm_rr_price: "CM RTRR",
            self._get_coinmarket_cap_price: "Coin Market Cap",
            self._get_coingecko_price: "CoinGecko"
        }

    def get_4pm_ny_report_header(self, with_sec: bool = False) -> str:
        ny_tz = pytz.timezone("America/New_York")
        dt: datetime.date = ny_tz.localize(datetime.datetime(year=self.report_date.year,
                                                             month=self.report_date.month,
                                                             day=self.report_date.day,
                                                             hour=16))
        if with_sec:
            template = '%Y-%m-%dT%H:%M:%S%z'
        else:
            template = '%Y-%m-%dT%H:%M%z'
        formatted_dt = dt.strftime(template)
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]

        return formatted_dt

    def generate_full_report(self) -> str:
        self._generate_report_body()
        report_header: str = textwrap.dedent(f"""
        All,
        Please see below for a copy of the Closing Levels for the CMBI Indices.
        Please feel free to reach out with any questions.
        Thank you.

        CMBI Close Report for {self.get_4pm_ny_report_header()}[America/New_York] [{self._bool_to_result(self.is_passed)}]
        """)
        report_footer: str = "\n\n\n\n\nPlease feel free to reach <NAME_EMAIL> with any questions."
        full_report = report_header + self.report_body + report_footer
        return full_report

    def _generate_report_body(self):
        self.report_body = ""
        index_data_dict: dict[str, float] = self._get_index_data_dictionary()
        log.info(f"index_data_dict at {self.report_date_query_param_16pm_ny}: {index_data_dict}")

        self._add_btc_indexes(index_data_dict)

        self.report_body += "\n"

        self._add_eth_indexes(index_data_dict)

        self.report_body += "\n"

        self._add_index_prices(indexes=self.SECONDARY_INDEXES, index_data_dict=index_data_dict)
        self._add_index_prices(indexes=self.ALL_12PM_UTC_INDEXES, index_data_dict=index_data_dict)

        self.report_body += "\n\n"

        self._add_index_prices(indexes=self.NETWORK_INDEXES, index_data_dict=index_data_dict)

        self.is_passed = True

    def _get_index_data_dictionary(self) -> dict[str:float]:
        index_data_dict: dict[str, float] = {}
        for index_name in self.ALL_16PM_NY_INDEXES:
            # Retrieving index 1 by 1 to avoid a single index API issue affecting all indexes in the response
            index_price = self._get_index_price(index_name=index_name, report_date_query_param=self.report_date_query_param_16pm_ny)
            index_data_dict[index_name] = index_price

        for index_name in self.ALL_12PM_UTC_INDEXES:
            index_price = self._get_index_price(index_name=index_name, report_date_query_param=self.report_date_query_param_12pm_utc)
            index_data_dict[index_name] = index_price

        return index_data_dict

    def _get_index_price(self, index_name: str, report_date_query_param: str) -> float:
        def _internal_get_index_price() -> float:
            index_data_frame: DataFrame = (client.get_index_levels(indexes=index_name,
                                                                   start_time=report_date_query_param,
                                                                   end_time=report_date_query_param,
                                                                   frequency="1h").to_dataframe())
            log.info(f"index_data_frame at {report_date_query_param}: {index_data_frame}")
            return index_data_frame.iloc[0]['level']

        try:
            return RetryUtil.retry_function(_internal_get_index_price)
        except Exception as e:
            log.error(msg=f"Error when requesting index price for {index_name} at {report_date_query_param}", exc_info=e)
            return 0.0

    def _add_btc_indexes(self, index_data_dict: dict):
        self._add_index_prices(indexes=self.BTC_INDEXES, index_data_dict=index_data_dict)

        self._external_source_checks(index_name="CMBIBTC",
                                      underlying_asset="btc",
                                      index_data_dict=index_data_dict,
                                      functions=[self._get_coinmarket_cap_price, self._get_cm_rr_price])

    def _add_eth_indexes(self, index_data_dict: dict):
        self._add_index_prices(indexes=self.ETH_INDEXES, index_data_dict=index_data_dict)

        self._external_source_checks(index_name="CMBIETH",
                                      underlying_asset="eth",
                                      index_data_dict=index_data_dict,
                                      functions=[self._get_coinmarket_cap_price, self._get_coingecko_price, self._get_cm_rr_price])

    def _add_index_prices(self, indexes: [str], index_data_dict: dict):
        for index in indexes:
            if index in index_data_dict:
                self.report_body += f"\n{index} price is {index_data_dict[index]}"
            else:
                log.warning(f"No price for asset {index} at {self.report_date_query_param_16pm_ny}")

    def _external_source_checks(self, index_name: str, underlying_asset: str, index_data_dict: dict, functions: [Callable[[str], float]]):
        if index_name in index_data_dict:
            index_price: float = index_data_dict[index_name]
            self.report_body += "\n"
            for function in functions:
                self._external_source_check(underlying_asset=underlying_asset, index_price=index_price, function=function)

    def _external_source_check(self, underlying_asset: str, index_price: float, function: Callable[[str], float]):
        name = self.function_to_names[function]
        external_price = function(underlying_asset)
        if external_price > 0.0:
            delta: float = self._calculate_delta(index_price, external_price)
            pass_str: str = self._bool_to_result(self._passed_external_source_check(delta))
            ext_price_str = f"{external_price:.6f}"
            delta_str: str = f"{delta * 100:.2f}"
            self.report_body += f"\nExternal Sources Check - Price Index > {name} : {pass_str} [ {ext_price_str} ({delta_str}%) ]"

    def _get_cm_rr_price(self, underlying_asset: str) -> float:
        def _internal_get_cm_rr_price() -> float:
            data = client.get_asset_metrics(assets=underlying_asset, metrics="ReferenceRateUSD",
                                            start_time=self.report_date_query_param_16pm_ny,
                                            end_time=self.report_date_query_param_16pm_ny,
                                            frequency="1s").first_page()
            reference_rate = data[-1]["ReferenceRateUSD"]
            return float(reference_rate)

        try:
            return RetryUtil.retry_function(_internal_get_cm_rr_price)
        except Exception as e:
            log.error(msg=f"Error when requesting RR for {underlying_asset} at {self.report_date_query_param_16pm_ny}", exc_info=e)
            return 0.0

    def _get_coinmarket_cap_price(self, underlying_asset: str) -> float:
        reader = CoinMarketCapPriceReader()

        def _internal_get_coinmarket_cap_price() -> float:
            price = reader.get_price(asset=underlying_asset)
            return price.price

        try:
            return RetryUtil.retry_function(_internal_get_coinmarket_cap_price)
        except Exception as e:
            log.error(msg=f"Error when requesting CMC price for {underlying_asset} at {self.report_date_query_param_16pm_ny}",
                         exc_info=e)
            return 0.0

    def _get_coingecko_price(self, underlying_asset: str) -> float:
        reader = CoinGeckoPriceReader()

        def _internal_get_coingecko_price() -> float:
            price = reader.get_price(asset=underlying_asset)
            return price.price

        try:
            return RetryUtil.retry_function(_internal_get_coingecko_price)
        except Exception as e:
            log.error(msg=f"Error when requesting CoinGecko price for {underlying_asset} at {self.report_date_query_param_16pm_ny}",
                         exc_info=e)
            return 0.0

    def _calculate_delta(self, index_price: float, external_price: float) -> float:
        return (external_price - index_price) / index_price

    def _passed_external_source_check(self, delta: float) -> bool:
        return abs(delta) < 0.025

    def _get_4pm_ny_query_param(self) -> str:
        """
        Gets the NY close time as a query param.
        """
        return self._get_hpm_query_param(16, "America/New_York")

    def _get_12pm_utc_query_param(self) -> str:
        """
        Gets the UTC 12pm time as a query param.
        """
        return self._get_hpm_query_param(12, "UTC")

    def _get_hpm_query_param(self, hour: int, timezone: str) -> str:
        ny_tz = pytz.timezone(timezone)
        dt = ny_tz.localize(datetime.datetime(year=self.report_date.year,
                                              month=self.report_date.month,
                                              day=self.report_date.day,
                                              hour=hour))
        return dt.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')

    def _bool_to_result(self, flag: bool) -> str:
        if flag:
            return 'PASSED'
        else:
            return 'FAILED'


if __name__ == '__main__':
    full_report_result = CMBINYCloseReporter(report_date=datetime.date(year=2023, month=10, day=31)).generate_full_report()
    print(f"full report:\n{full_report_result}")
