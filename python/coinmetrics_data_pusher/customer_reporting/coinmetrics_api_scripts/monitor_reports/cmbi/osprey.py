import datetime

import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.cmbi.database_trade_reader import \
    DatabaseTradeReader
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("OspreyEmailReport")

client = CoinMetricsClientFactory.get_client()


class OspreyEmailReport:

    def __init__(self, report_date: datetime.date):
        self.report_date = report_date
        eastern_tz = pytz.timezone("America/New_York")
        self.report_time = eastern_tz.localize(datetime.datetime(year=report_date.year,
                                                   month=report_date.month,
                                                   day=report_date.day, hour=16))

    def get_formatted_report_time(self) -> str:
        formatted_dt = self.report_time.strftime('%Y-%m-%dT%H:%M%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]
        return formatted_dt

    def get_4pm_est_query_parm(self) -> str:
        """
        Gets the report time as a time that queries the API easily
        """
        report_time_param = self.report_time.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
        return report_time_param

    def create_index_levels_string(self) -> str:
        """
        This function generates the portion of the email that looks like:
        CMBI Bitcoin (CMBIBTC) price is: 30864.6664006429
        CMBI Ethereum (CMBIETH) price is: 1901.74777323203
        """
        report_time = self.get_4pm_est_query_parm()
        cmbi_btc_price = client.get_index_levels(indexes="CMBIBTC", start_time=report_time, end_time=report_time, frequency="1d-ny-close").to_list()[-1]['level']
        cmbi_eth_price = client.get_index_levels(indexes="CMBIETH", start_time=report_time, end_time=report_time, frequency="1d-ny-close").to_list()[-1]['level']
        result_string = f"""
CMBI Bitcoin (CMBIBTC) price is: {cmbi_btc_price}
CMBI Ethereum (CMBIETH) price is: {cmbi_eth_price}
"""
        return result_string

    def create_exchange_levels_string(self) -> str:
        """
        Creates a string of prices from exchanges based on customer request
        """
        result = ""
        exchange_assets_dict = {
            "Binance.US": {
                "BNB": ["USD", "USDT"],
                "HNT": ["USD"],
            },
            "Binance": {
                "TON": ["USDT"]
            },
            "Bitfinex": {
                "AVAX": ["USD"]
            },
            "Bittrex": {
                "CELO": ["USD"]
            },
            "Coinbase": {
                "BTC": ["USD"],
                "ETH": ["USD"],
                "ADA": ["USD"],
                "ALGO": ["USD"],
                "BAT": ["USD"],
                "DOT": ["USD"],
                "MATIC": ["USD"],
                "SOL": ["USD"],
                "AVAX": ["USD"],
                "CELO": ["USD"],
                "XTZ": ["USD"],
                "KSM": ["USD"],
                "BONK": ["USD"]
            },
            "Kraken": {
                "BTC": ["USD"],
                "ADA": ["USD"],
                "ALGO": ["USD"],
                "BAT": ["USD"],
                "DOT": ["USD"],
                "MATIC": ["USD"],
                "SOL": ["USD"],
                "AVAX": ["USD"],
                "CELO": ["USD"]
            },
        }
        database_reader = DatabaseTradeReader(report_date=self.report_date)
        for exchange, assetToQuotes in exchange_assets_dict.items():
            for asset, quotes in assetToQuotes.items():
                for quote in quotes:
                    exchange_asset_price = database_reader.read_close_trade(exchange=exchange, base=asset, quote=quote)
                    quote_part = "" if quote == "USD" else f"-{quote}"
                    new_line = f"{exchange} {asset}{quote_part} {'--' if not exchange_asset_price else exchange_asset_price}"
                    result += new_line + "\n"
            if exchange == "Coinbase":
                result += "\n\n\n"
            else:
                result += "\n"
        return result

    def generate_full_report(self):
        """
        Creates Osprey report for given date. This report came from monitor project and is sent to the user daily at 4pm est
        Example of file output can be found in ../../tests/files/osprey....
        :param report_date: date of the data for report. Data will come from 4pm on that day.
        :return: str txt formatted of the osprey email. Should be converted to html for customer.
        """
        log.info(f"{self.__class__.__name__} generating a report for the date {self.report_date} and time {self.report_time}")
        result = ""

        report_header = f"""All,

Please see below for a copy of today's closing levels.
Please feel free to reach out with any questions.
Thank you.

CMBI Index report for: {self.get_formatted_report_time()}[America/New_York]
{self.create_index_levels_string()}
Please feel free to reach <NAME_EMAIL> with any questions.

{self.create_exchange_levels_string()}
"""
        result += report_header

        return result


if __name__ == '__main__':
    report_date = datetime.date(year=2024, month=9, day=8)
    print(OspreyEmailReport(report_date=report_date).generate_full_report())
