import pytz
import datetime

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
import datetime

import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()


class CMBISingaporeCloseReporter:
    """
    Creates report like:
All,
Please see below for a copy of the Closing Levels for the CMBI Bitcoin Index.
Please feel free to reach out with any questions.
Thank you.

CMBI Bitcoin Index report for: 2023-05-12T17:00+08:00[Asia/Singapore]
Price Index : [ 26316.7462694331 ]
TR Index : [ 28274.40507544996 ]
    """

    def __init__(self, report_date: datetime.date = datetime.date.today()):
        self.report_date = report_date

    def get_4pm_singapore_report_header(self) -> str:
        singapore_tz = pytz.timezone("Asia/Singapore")
        dt = singapore_tz.localize(datetime.datetime(year=self.report_date.year,
                                                  month=self.report_date.month,
                                                  day=self.report_date.day, hour=17))
        formatted_dt = dt.strftime('%Y-%m-%dT%H:%M:%S%z')
        formatted_dt = formatted_dt[:-2] + ':' + formatted_dt[-2:]
        return formatted_dt

    def get_4pm_singapore_query_param(self) -> str:
        """
        Gets the london close price for all assets
        """
        london_tz = pytz.timezone("Asia/Singapore")
        four_pm_singapore = london_tz.localize(datetime.datetime(year=self.report_date.year,
                                                              month=self.report_date.month,
                                                              day=self.report_date.day, hour=17))
        four_pm_singapore_utc = four_pm_singapore.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
        return four_pm_singapore_utc


    def generate_full_report(self) -> str:
        report_header = f"""
All,
Please see below for a copy of the Closing Levels for the CMBI Bitcoin Index.
Please feel free to reach out with any questions.
Thank you.

CMBI Bitcoin Index Report for: {self.get_4pm_singapore_report_header()}[Asia/Singapore]        
"""
        singapore_4pm = self.get_4pm_singapore_query_param()
        close_prices = client.get_index_levels(indexes=['CMBIBTC', 'CMBIBTCT'], start_time=singapore_4pm, end_time=singapore_4pm, frequency="1h").to_dataframe()
        close_price_price = close_prices[close_prices['index']=="CMBIBTC"]['level'].iloc[0]
        close_price_total = close_prices[close_prices['index']=="CMBIBTCT"]['level'].iloc[0]
        report_body = f"Price Index:[ {close_price_price} ]\nTR Index:[ {close_price_total} ]"
        report_footer = "\n\nPlease feel free to reach <NAME_EMAIL> with any questions"
        full_report = report_header + report_body + report_footer
        return full_report


if __name__ == '__main__':
    print(CMBISingaporeCloseReporter().generate_full_report())