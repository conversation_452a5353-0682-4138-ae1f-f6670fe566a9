import os

import pytz
import datetime
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_constituents_report import generate_fidelity_constituents_report_df

def generate_constituents_report_locally(directory_path: str = ".") -> str:
    """
    This function generates fidelity constituents file locally. It is meant to be used in the case that the standard report
    failed to out for the day. Mostly just wraps the generate_constituents_report_locally.
    :param directory_path: where to create the file. By default will be in local diretory.
    :return str: of the path where the file is genereated
    """
    ny_tz = pytz.timezone("America/New_York")
    ny_time = datetime.datetime.now(ny_tz)
    report_date = datetime.date.today() if ny_time.hour >= 16 and ny_time.date() == datetime.date.today() else datetime.date.today() - datetime.timedelta(
        days=1)
    data_df = generate_fidelity_constituents_report_df(reporting_date=report_date)
    file_path = os.path.join(directory_path, f"fidelity-constituents-{report_date.strftime('%y-%m-%d')}")
    data_df.to_csv(file_path, index=False)
    print(f"File created at: {file_path}")
    return file_path


if __name__ == '__main__':
    generate_constituents_report_locally()