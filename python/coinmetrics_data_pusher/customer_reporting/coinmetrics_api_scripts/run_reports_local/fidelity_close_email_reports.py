import datetime
import os

import pytz
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.check_close import IndexReporter

def generate_fidelity_email_reports_locally() -> str:
    """
    Generates a string for the daily fidelity email close repors for FIDETH and FIDBTC. This is intended as a backup
    script that can be run locally in the case that there is an issue with production, and someone needs to quickly
    generate and send these reports.
    :param report_date: date for report
    :param report_hour: hour for the report (eastern). Generally will be 16 (4pm) unless there is a reason to run it
    differently like on early market closes
    :return: str of the two reports seperated by a ---- line
    """
    if not os.environ.get("ENVIRONMENT"):
        os.environ["ENVIRONMENT"] = "STAGING"
    result = ""
    ny_tz = pytz.timezone("America/New_York")
    ny_time = datetime.datetime.now(ny_tz)
    report_date = datetime.date.today() if ny_time.hour >= 16 and ny_time.date() == datetime.date.today() else datetime.date.today() - datetime.timedelta(
        days=1)
    eth_reporter = IndexReporter(index="FIDETH", report_date=report_date, hour=16)
    btc_reporter = IndexReporter(index="FIDBTC", report_date=report_date, hour=16)
    eth_email = eth_reporter.create_close_report()
    btc_email = btc_reporter.create_close_report()
    result += eth_email + "\n"
    result += "-" * 60 + "\n"
    result += btc_email
    return result


if __name__ == '__main__':
    # Prints report to console and writes file fidelity_email_reports.txt
    fidelity_reports_str = generate_fidelity_email_reports_locally()
    print(generate_fidelity_email_reports_locally())
    with open(f"fidelity_email_reports.txt", "w") as f:
        f.write(fidelity_reports_str)

