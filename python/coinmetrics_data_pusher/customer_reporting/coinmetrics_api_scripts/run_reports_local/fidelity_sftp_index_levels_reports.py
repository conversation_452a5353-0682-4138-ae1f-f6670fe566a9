import datetime
from typing import List

import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_index_exporters import \
    SingleAssetFidelityPair, MultiAssetFidelityPair, FidelityEvenIndexPair


def generate_realtime_index_levels_reports(directory_path: str = ".") -> List[str]:
    """
    Generates realtime 4pm reports for the fidelity files fidbe, fidebe, fidbtc and fideth that are dropped in sftp
    each day. This is intended to be used in case there is an issue with production, or otherwise for local testing.
    :param directory_path: path where files should be created
    :return List[str]: list of files generated
    """
    timezone = pytz.timezone("America/New_York")
    fidbtc_file_prefix = "fidbtc"
    report_date = datetime.date.today() if datetime.datetime.now(
        timezone).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
    fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
    fidbtc_index_report_file_name = fidbtc_report_generator.generate_realtime_index_report(directory_path,
                                                                                           fidbtc_file_prefix)

    fideth_file_prefix = "fideth"
    fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date)
    fideth_index_report_file_name = fideth_report_generator.generate_realtime_index_report(directory_path,
                                                                                           fideth_file_prefix)

    fidbe_file_prefix = "fidebe"
    fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date)
    fidbe_index_report_file_name = fidbe_report_generator.generate_realtime_index_report(directory_path,
                                                                                         fidbe_file_prefix)

    fidbei_file_prefix = "fidbei"
    fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
    fidbei_index_report_file_name = fidbei_report_generator.generate_realtime_index_report(directory_path,
                                                                                           fidbei_file_prefix)

    all_files = [fidbtc_index_report_file_name, fideth_index_report_file_name, fidbe_index_report_file_name,
                 fidbei_index_report_file_name]
    print(f"Files generated: {all_files}")
    return all_files


def generate_daily_index_reports(directory_path: str = ".") -> List[str]:
    """
    Generates daily 4pm reports for the fidelity files fidbe, fidebe, fidbtc and fideth that are dropped in sftp
    each day. This is intended to be used in case there is an issue with production, or otherwise for local testing.
    :param directory_path: path where files will be created
    :return List[str]: list of files generated
    """
    timezone = pytz.timezone("America/New_York")
    fidbtc_file_prefix = "fidbtc"
    report_date = datetime.date.today() if datetime.datetime.now(
        timezone).hour >= 16 else datetime.date.today() - datetime.timedelta(days=1)
    fidbtc_report_generator = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=report_date)
    fidbtc_index_report_file_name = fidbtc_report_generator.generate_daily_index_report(directory_path,
                                                                                        fidbtc_file_prefix)

    fideth_file_prefix = "fideth"
    fideth_report_generator = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=report_date)
    fideth_index_report_file_name = fideth_report_generator.generate_daily_index_report(directory_path,
                                                                                        fideth_file_prefix)

    fidbe_file_prefix = "fidebe"
    fidbe_report_generator = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=report_date)
    fidbe_index_report_file_name = fidbe_report_generator.generate_daily_index_report(directory_path,
                                                                                      fidbe_file_prefix)

    fidbei_file_prefix = "fidbei"
    fidbei_report_generator = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=report_date)
    fidbei_index_report_file_name = fidbei_report_generator.generate_daily_index_report(directory_path,
                                                                                        fidbei_file_prefix)

    all_files = [fidbtc_index_report_file_name, fideth_index_report_file_name, fidbe_index_report_file_name,
                 fidbei_index_report_file_name]
    print(f"Generated files: {all_files}")
    return all_files


if __name__ == '__main__':
    print(f"Generating Fidelity realtime SFTP reports")
    generate_realtime_index_levels_reports()
    print(f"Generating daily index reports")
    generate_daily_index_reports()
