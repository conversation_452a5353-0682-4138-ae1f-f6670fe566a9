import datetime
import os

import numpy as np
import pytest
import pandas as pd
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.cmbi_export.grayscale_export import create_grayscale_report


def test_grayscale_report_april_30() -> None:
    """
    Tests that file generated by grayscale script matches what we sent client April 30th 2023
    """
    expected_file = pd.read_csv("files/grayscale/cm_rates_april_30_2023.csv", header=None)
    expected_file.iloc[:, 2] = expected_file.iloc[:, 2].apply(lambda num: np.round(num, decimals=8))
    actual_file_path = create_grayscale_report(report_date=datetime.date(year=2023, month=4, day=30), folder_location=".")
    actual_file_df = pd.read_csv(actual_file_path, header=None)
    actual_file_df.iloc[:, 2] = actual_file_df.iloc[:, 2].apply(lambda num: np.round(num, decimals=8))
    try:
        assert expected_file.equals(actual_file_df)
        os.remove(actual_file_path)
    except AssertionError as e:
        os.remove(actual_file_path)
        raise e


if __name__ == '__main__':
    pytest.main()