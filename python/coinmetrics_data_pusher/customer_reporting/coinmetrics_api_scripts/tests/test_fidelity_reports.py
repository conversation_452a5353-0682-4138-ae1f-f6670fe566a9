import datetime

import numpy as np
import pandas as pd
import pytest

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_index_exporters \
    import FidelityEvenIndexPair, SingleAssetFidelityPair, \
    MultiAssetFidelityPair


def heuristic_compare_dfs(expected_df: pd.DataFrame, actual_df: pd.DataFrame, **kwargs) -> None:
    """
    This function uses the heuristic of checking the 1st, 100th, 500th and final rows of a dataframe in order to determine
    equality. This is because actually using pd.DataFrame.equals() is not really viable for this testing
    """
    rows_to_check = [1, 100, 500, -1]
    for row in rows_to_check:
        compare_rows(expected_df.iloc[row], actual_df.iloc[row], **kwargs)


def compare_rows(expected_row: pd.Series, actual_row: pd.Series, round_index_price: int = 15,
                 round_total_return: int = 10, round_market_value: int = 10) -> None:
    assert np.round(expected_row.index_price, decimals=round_index_price) == \
           np.round(actual_row.index_price, decimals=round_index_price)
    assert expected_row.index_time == str(actual_row.index_time)
    assert np.round(expected_row.total_return, decimals=round_total_return) == \
           np.round(actual_row.total_return, decimals=round_total_return)
    if 'market_value' in expected_row:
        if expected_row.market_value is None or expected_row.market_value is None:
            market_value_expected = np.round(expected_row.market_value, decimals=round_market_value)
            market_value_actual = np.round(actual_row.market_value, decimals=round_market_value)
            assert market_value_expected == market_value_actual

def test_fidbtc_jan_23() -> None:
    """
    Tests that python implementation matches production fidbtc-2023-01-23.csv from SFTP server
    """
    production_file = pd.read_csv("files/fidbtc-2023-01-23.csv")
    reporting_date = datetime.date(year=2023, month=1, day=23)
    fidbtc_pair = SingleAssetFidelityPair(price_index="FIDBTCP", return_index="FIDBTCT", reporting_date=reporting_date,
                                          underlying_asset="btc")
    data_df = fidbtc_pair.generate_daily_index_report_df()
    heuristic_compare_dfs(production_file, data_df)


def test_fideth_jan_21() -> None:
    """
    Tests that python implementation matches production fideth-2023-01-21.csv from SFTP server
    """
    production_file = pd.read_csv("files/fideth-2023-01-21.csv")
    reporting_date = datetime.date(year=2023, month=1, day=21)
    fideth_pair = SingleAssetFidelityPair(price_index="FIDETHP", return_index="FIDETHT", reporting_date=reporting_date,
                                          underlying_asset="eth")
    data_df = fideth_pair.generate_daily_index_report_df()
    heuristic_compare_dfs(production_file, data_df)


def test_fidbei_jan_23() -> None:
    """
    Tests that python implementation matched production fidebe-2023-01-22.csv from SFTP server
    """
    production_file = pd.read_csv("files/fidbei-2023-01-23.csv")
    reporting_date = datetime.date(year=2023, month=1, day=23)
    fidbei_pair = MultiAssetFidelityPair(price_index="FIDBEIP", return_index="FIDBEIT", reporting_date=reporting_date)
    data_df = fidbei_pair.generate_daily_index_report_df()
    heuristic_compare_dfs(production_file, data_df, round_market_value=3)


def test_fidbe_jan_22() -> None:
    """
    Tests that python implementation matched production fidebe-2023-01-22.csv from SFTP server
    """
    production_file = pd.read_csv("files/fidebe-2023-01-22.csv")
    reporting_date = datetime.date(year=2023, month=1, day=22)
    fidbe_pair = FidelityEvenIndexPair(price_index="FIDEBEP", return_index="FIDEBET", reporting_date=reporting_date)
    data_df = fidbe_pair.generate_daily_index_report_df()
    heuristic_compare_dfs(production_file, data_df, round_market_value=3, round_total_return=8)


def test_fidbtc_t16_jan23() -> None:
    """
    Tests that python implementation matches production fidbtc-2023-01-23T1600-0500.csv fom SFTP server
    """
    production_file = pd.read_csv("files/fidbtc-2023-01-23T1600-0500.csv")
    reporting_date = datetime.date(year=2023, month=1, day=23)
    fidbtc_pair = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=reporting_date)
    data_df = fidbtc_pair.generate_realtime_index_report_df()
    heuristic_compare_dfs(production_file, data_df)

def test_fidbtc_t16_jan23_fail() -> None:
    """
    Test should fail - indicates testing is working as expected
    """
    production_file = pd.read_csv("files/fidbtc-2023-01-23T1600-0500.csv")
    reporting_date = datetime.date(year=2023, month=1, day=24)
    fidbtc_pair = SingleAssetFidelityPair.create_fidbtc_reporter(reporting_date=reporting_date)
    data_df = fidbtc_pair.generate_realtime_index_report_df()
    try:
        heuristic_compare_dfs(production_file, data_df)
    except Exception as e:
        assert type(e) == AssertionError


def test_fideth_t16_jan_22() -> None:
    """
    Tests that python implementation matches the fideth-2023-01-22T1600-0500.csv file from SFTP server
    """
    production_file = pd.read_csv("files/fideth-2023-01-22T1600-0500.csv")
    reporting_date = datetime.date(year=2023, month=1, day=22)
    fideth_reporter = SingleAssetFidelityPair.create_fideth_reporter(reporting_date=reporting_date)
    data_df = fideth_reporter.generate_realtime_index_report_df()
    heuristic_compare_dfs(production_file, data_df)


def test_fidebe_t16_jan22() -> None:
    """
    Tests that python implementation matches fidebe-2023-01-22T1600-0500.csv from SFTP server
    """
    production_file = pd.read_csv("files/fidebe-2023-01-22T1600-0500.csv")
    reporting_date = datetime.date(year=2023, month=1, day=22)
    fidebe_reporter = FidelityEvenIndexPair.create_fidebe_reporter(reporting_date=reporting_date)
    data_df = fidebe_reporter.generate_realtime_index_report_df()
    heuristic_compare_dfs(production_file, data_df, round_total_return=9)


def test_fidbei_t16_jan_22() -> None:
    """
    Tests that the python implementation matches fidbei-2023-01-22T1600-0500.csv from SFTP server
    """
    production_file = pd.read_csv("files/fidbei-2023-01-22T1600-0500.csv")
    reporting_date = datetime.date(year=2023, month=1, day=22)
    fidbei_reporter = MultiAssetFidelityPair.create_fidbei_reporter(reporting_date=reporting_date)
    data_df = fidbei_reporter.generate_realtime_index_report_df()
    heuristic_compare_dfs(production_file, data_df, round_total_return=9)


if __name__ == '__main__':
    pytest.main()
