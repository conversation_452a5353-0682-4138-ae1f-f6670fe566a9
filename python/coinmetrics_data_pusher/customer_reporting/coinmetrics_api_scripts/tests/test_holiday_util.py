import unittest
from datetime import datetime

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.holiday_util import is_july_3_a_weekday, \
    is_an_after_thanksgiving_day, is_december_24_a_weekday, is_jimmy_carter_honor_a_weekday, \
    is_martin_luther_king_jr_day, is_memorial_day, is_july_4_a_weekday


class HolidayUtilTest(unittest.TestCase):
    def test_is_july_3_a_weekday(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        # and https://www.calendar-365.com/holidays/independence-day.html
        source = [
            ('2023-07-02', False),
            ('2023-07-03', True),  # Monday (July 4, 2023 Independence Day 2023 Tuesday)
            ('2023-07-04', False),
            ('2024-06-03', False),
            ('2024-07-03', True),  # Wednesday (July 4, 2024 Independence Day 2024 Thursday)
            ('2024-08-03', False),
            ('2025-07-03', True),  # Thursday (July 4, 2025 Independence Day 2025 Friday)
            ('2026-07-03', True),  # Friday (July 4, 2026 Independence Day 2026 Saturday)
            ('2027-07-03', False),  # Saturday (July 4, 2027 Independence Day 2027 Sunday)
            ('2028-07-03', True),  # Monday (July 4, 2028 Independence Day 2028 Tuesday)
            ('2029-07-03', True),  # Tuesday (July 4, 2029 Independence Day 2029 Wednesday)
            ('2030-07-03', True)  # Wednesday (July 4, 2030 Independence Day 2030 Thursday)
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_july_3_a_weekday(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])

    def test_is_july_4_a_weekday(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        # and https://www.calendar-365.com/holidays/independence-day.html
        source = [
            ('2023-07-02', False),
            ('2023-07-03', False),
            ('2023-07-04', True),  # Tuesday (July 4, 2023 Independence Day)
            ('2024-06-03', False),
            ('2024-07-03', False),
            ('2024-07-04', True),  # Thursday (July 4, 2024 Independence Day)
            ('2024-08-03', False),
            ('2025-07-03', False),
            ('2025-07-04', True),  # Friday (July 4, 2025 Independence Day)
            ('2026-07-03', False),
            ('2026-07-04', False),  # Saturday (July 4, 2026 Independence Day)
            ('2027-07-03', False),
            ('2027-07-04', False),  # Sunday (July 4, 2027 Independence Day)
            ('2028-07-03', False),
            ('2028-07-04', True),  # Tuesday (July 4, 2028 Independence Day)
            ('2029-07-03', False),
            ('2029-07-04', True),  # Wednesday (July 4, 2029 Independence Day)
            ('2030-07-03', False),
            ('2030-07-04', True)  # Thursday (July 4, 2030 Independence Day)
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_july_4_a_weekday(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])

    def test_is_an_after_thanksgiving_day(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        # and https://www.qppstudio.net/global-holidays-observances/us-thanksgiving.htm
        source = [
            #   {
            #     "date": "2023-11-23",
            #     "name": "Thanksgiving Day",
            #     "schedule": "Closed"
            #   },
            ('2023-11-24', True),  # Thanksgiving 2023: November 23
            ('2023-11-25', False),
            ('2023-11-26', False),

            #   {
            #     "date": "2024-11-28",
            #     "name": "Thanksgiving Day",
            #     "schedule": "Closed"
            #   },
            ('2024-11-29', True),  # Thanksgiving 2024: November 28
            ('2024-11-30', False),
            ('2024-12-30', False),

            #   {
            #     "date": "2025-11-27",
            #     "name": "Thanksgiving Day",
            #     "schedule": "Closed"
            #   },
            ('2025-11-28', True),  # Thanksgiving 2025: November 27
            ('2025-11-29', False),
            ('2025-11-30', False),

            ('2026-11-27', True),  # Thanksgiving 2026: November 26
            ('2026-11-28', False),
            ('2026-11-29', False),

            ('2027-11-26', True),  # Thanksgiving 2027: November 25
            ('2027-11-27', False),

            ('2028-11-24', True),  # Thanksgiving 2028: November 23
            ('2028-11-25', False),

            ('2029-11-23', True),  # Thanksgiving 2029: November 22
            ('2029-11-24', False),

            ('2030-11-29', True),  # Thanksgiving 2030: November 28
            ('2030-11-30', False)
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_an_after_thanksgiving_day(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])

    def test_is_december_24_a_weekday(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        source = [
            ('2023-12-23', False),
            #   {
            #     "date": "2023-12-25",
            #     "name": "Christmas",
            #     "schedule": "Closed"
            #   },
            ('2023-12-24', False),  # Sunday
            ('2023-12-25', False),
            ('2024-12-23', False),
            #   {
            #     "date": "2024-12-25",
            #     "name": "Christmas",
            #     "schedule": "Closed"
            #   },
            ('2024-12-24', True),  # Tuesday
            ('2024-12-25', False),
            ('2025-11-24', False),
            #   {
            #     "date": "2025-12-25",
            #     "name": "Christmas",
            #     "schedule": "Closed"
            #   }
            ('2025-12-24', True),  # Wednesday
            ('2026-12-24', True),  # Thursday
            ('2027-12-24', True),  # Friday
            ('2028-12-24', False),  # Sunday
            ('2029-12-24', True),  # Monday
            ('2030-12-24', True),  # Tuesday
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_december_24_a_weekday(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])

    def test_is_martin_luther_king_jr_day(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        # and https://print-a-calendar.com/holiday/martin-luther-king-day
        source = [
            ('2025-01-19', False),
            ('2025-01-20', True), # Martin Luther King Jr. Day 2025: January 20
            ('2025-01-21', False),

            ('2026-01-18', False),
            ('2026-01-19', True), # Martin Luther King Jr. Day 2026: January 19
            ('2026-01-20', False),

            ('2027-01-17', False),
            ('2027-01-18', True), # Martin Luther King Jr. Day 2027: January 18
            ('2027-01-19', False),

            ('2028-01-16', False),
            ('2028-01-17', True), # Martin Luther King Jr. Day 2028: January 17
            ('2028-01-18', False),

            ('2029-01-14', False),
            ('2029-01-15', True), # Martin Luther King Jr. Day 2029: January 15
            ('2029-01-16', False),

            ('2030-01-20', False),
            ('2030-01-21', True), # Martin Luther King Jr. Day 2030: January 21
            ('2030-01-22', False),
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_martin_luther_king_jr_day(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])


    def test_is_jimmy_carter_honor_a_weekday(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        source = [
            ('2024-01-08', False),
            ('2024-01-09', False),
            ('2024-01-10', False),
            ('2025-01-08', False),
            ('2025-01-09', True),
            ('2025-01-10', False),
            ('2026-01-08', False),
            ('2026-01-09', False),
            ('2026-01-10', False),

        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_jimmy_carter_honor_a_weekday(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])


    def test_is_memorial_day_last_monday_in_may(self):
        # checked against https://gitlab.com/coinmetrics/resources/-/blob/master/holidays_nyse.json?ref_type=heads
        source = [
            ('2025-05-25', False),
            ('2025-05-26', True),
            ('2025-05-27', False),
            ('2026-05-24', False),
            ('2026-05-25', True),
            ('2026-05-26', False),
            ('2027-05-31', True),
            ('2028-05-29', True),
            ('2029-05-07', False),
            ('2029-05-28', True),
            ('2030-05-20', False),
            ('2030-05-27', True),
        ]
        for dt_str_to_result in source:
            date_time = datetime.strptime(dt_str_to_result[0], '%Y-%m-%d')
            self.assertEqual(is_memorial_day(date_time), dt_str_to_result[1], 'assertion failed for ' + dt_str_to_result[0])


if __name__ == '__main__':
    unittest.main()
