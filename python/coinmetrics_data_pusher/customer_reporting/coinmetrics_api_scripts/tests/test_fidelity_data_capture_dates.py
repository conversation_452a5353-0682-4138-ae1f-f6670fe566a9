import datetime
import pytest

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_date_resolver import FidelityDateResolver
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_constituents_report import FidelityConstituentReportMultiAssetPair, ProformaConstituentsReport

def test_data_capture_date_may_30() -> None:
    """
    This tests the class calculates the data capture date for May 30th 2023 as March 2nd 2023 as expected
    """
    expected = datetime.date(year=2023, month=3, day=2)
    input_date = datetime.date(year=2023, month=5, day=30)
    actual = FidelityDateResolver.get_previous_capture_date_or_fail(report_date=input_date)
    assert actual == expected

def test_data_capture_date_june_5th() -> None:
    """
    This tests the class calculates the data capture date for June 6th 2023 as March 2nd 2023 as expected
    """
    expected = datetime.date(year=2023, month=3, day=2)
    input_date = datetime.date(year=2023, month=6, day=5)
    actual = FidelityDateResolver.get_previous_capture_date_or_fail(report_date=input_date)
    assert actual == expected


def test_data_capture_date_june_20th() -> None:
    """
    This tests the class calculates the data capture date for June 20th 2023 as June 1st 2023 as expected
    """
    expected = datetime.date(year=2023, month=6, day=1)
    input_date = datetime.date(year=2023, month=6, day=16)
    actual = FidelityDateResolver.get_previous_capture_date_or_fail(report_date=input_date)
    assert actual == expected


def test_proforma_data_capture_date_march_5th() -> None:
    """
    Tests that march 5th 2023 was a proforma date ( it was )
    """
    input_date = datetime.date(year=2023, month=3, day=3)
    actual = FidelityDateResolver.is_in_proforma_reporting_window(report_date=input_date)
    assert actual == True


def test_proforma_data_capture_date_march_2nd() -> None:
    """
    Tests that March 2nd 2023 was not in the proforma window because this is a data capture date, it should start the 3rd
    """
    input_date = datetime.date(year=2023, month=3, day=2)
    actual = FidelityDateResolver.is_in_proforma_reporting_window(report_date=input_date)
    assert actual == False


def test_proforma_data_capture_date_june2() -> None:
    """
    Tests that June 2nd 2023 is a proforma date
    """
    input_date = datetime.date(year=2023, month=6, day=2)
    actual = FidelityDateResolver.is_in_proforma_reporting_window(report_date=input_date)
    assert actual == True


def test_proforma_data_capture_date_june15() -> None:
    """
    Tests that June 15th 2023 is a proforma date
    """
    input_date = datetime.date(year=2023, month=6, day=15)
    actual = FidelityDateResolver.is_in_proforma_reporting_window(report_date=input_date)
    assert actual == True


def test_proforma_data_capture_date_june16() -> None:
    """
    Tests that June 16th 2023 is  not a proforma date
    """
    input_date = datetime.date(year=2023, month=6, day=16)
    actual = FidelityDateResolver.is_in_proforma_reporting_window(report_date=input_date)
    assert actual == False


def test_data_capture_proforma_june_2nd() -> None:
    expected = datetime.date(year=2023, month=6, day=1)
    input_date = datetime.date(year=2023, month=6, day=2)
    actual = FidelityDateResolver.resolve_capture_date_or_fail(report_date=input_date)
    assert actual == expected


def test_data_capture_proforma_march_3rd() -> None:
    expected = datetime.date(year=2023, month=3, day=2)
    input_date = datetime.date(year=2023, month=3, day=3)
    actual = FidelityDateResolver.resolve_capture_date_or_fail(report_date=input_date)
    assert actual == expected


if __name__ == '__main__':
    pytest.main()
