import os
import numpy as np
import pytest
import datetime
import pandas as pd
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.cmbi_export.cmbi_indexes_export import generate_cmbi_eth_file_rt, generate_cmbi_btc_file_rt, generate_cmbi_eth_close, generate_cmbi_btc_close_file

TESTING_DATE = datetime.date(year=2023, month=3, day=20)
ROUNDING_FUNC = lambda num: np.round(num, decimals=8)

def test_generated_file_matches_expected_btc_rt() -> None:
    """
    Checks the file from may 20th 2023 versus newly generated one. Asserts there is max one difference. In this case it
    accounts for one rounding difference at ~10 decimal places
    """
    generated_file = generate_cmbi_btc_file_rt(report_date=TESTING_DATE, output_directory=".")
    generated_file_df = pd.read_csv(generated_file)
    expected_file = pd.read_csv("files/cmbi-btc-rt-2023-03-20T1600-0400.csv")
    generated_file_df['total_return'] = generated_file_df['total_return'].apply(ROUNDING_FUNC)
    expected_file['total_return'] = expected_file['total_return'].apply(ROUNDING_FUNC)
    compare = generated_file_df.compare(expected_file)
    try:
        assert len(compare) < 1
    except AssertionError as e:
        os.remove(generated_file)
        raise e


def test_generated_file_matches_expected_eth_rt() -> None:
    """
    Checks the file from may 20th 2023 versus newly generated one. Asserts there is max one difference. In this case it
    accounts fo rounding differences at ~10 decimal places
    """
    generated_file = generate_cmbi_eth_file_rt(report_date=TESTING_DATE, output_directory=".")
    generated_file_df = pd.read_csv(generated_file)
    expected_file = pd.read_csv("files/cmbi-eth-rt-2023-03-20T1600-0400.csv")
    generated_file_df['total_return'] = generated_file_df['total_return'].apply(ROUNDING_FUNC)
    expected_file['total_return'] = expected_file['total_return'].apply(ROUNDING_FUNC)
    compare = generated_file_df.compare(expected_file)
    try:
        assert len(compare) < 2
        os.remove(generated_file)
    except AssertionError as e:
        os.remove(generated_file)
        raise e


def test_generated_file_matches_expected_eth_close() -> None:
    """
    Checks the file from may 20th 2023 versus newly generated one. Asserts there is max one difference. In this case it
    accounts fo rounding differences at ~10 decimal places
    """
    generated_file = generate_cmbi_eth_close(report_date=TESTING_DATE, output_directory=".")
    generated_file_df = pd.read_csv(generated_file)
    generated_file_df['total_return'] = generated_file_df['total_return'].apply(ROUNDING_FUNC)
    expected_file = pd.read_csv("files/cmbi-eth-close-2023-03-20T1600-0400.csv")
    expected_file['total_return'] = expected_file['total_return'].apply(ROUNDING_FUNC)
    compare = generated_file_df.compare(expected_file)
    try:
        assert len(compare) < 2
        os.remove(generated_file)
    except AssertionError as e:
        os.remove(generated_file)
        raise e


def test_generated_file_matches_expected_btc_close() -> None:
    """
    Checks the file from may 20th 2023 versus newly generated one. Asserts there is max one difference. In this case it
    accounts fo rounding differences at ~10 decimal places
    """
    generated_file = generate_cmbi_eth_close(report_date=TESTING_DATE, output_directory=".")
    generated_file_df = pd.read_csv(generated_file)
    generated_file_df['total_return'] = generated_file_df['total_return'].apply(ROUNDING_FUNC)
    expected_file = pd.read_csv("files/cmbi-btc-close-2023-03-20T1600-0400.csv")
    expected_file['total_return'] = expected_file['total_return'].apply(ROUNDING_FUNC)
    compare = generated_file_df.compare(expected_file)
    try:
        assert len(compare) < 2
        os.remove(generated_file)
    except AssertionError as e:
        os.remove(generated_file)
        raise e



if __name__ == '__main__':
    pytest.main()