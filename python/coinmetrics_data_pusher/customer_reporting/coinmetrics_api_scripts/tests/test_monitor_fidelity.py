import pytest
import numpy as np
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.monitor_reports.fidelity.external_price_readers import CoinMarketCapPriceReader, CoinGeckoPriceReader


def test_coingecko_reader() -> None:
    coingecko_reader = CoinGeckoPriceReader()
    btc_price = coingecko_reader.get_price("btc")
    eth_price = coingecko_reader.get_price("eth")
    assert btc_price.price > 5000
    assert eth_price.price > 100
    assert btc_price.name == "btc"
    assert eth_price.name == "eth"


def test_cmc_reader() -> None:
    coingecko_reader = CoinMarketCapPriceReader()
    btc_price = coingecko_reader.get_price("btc")
    eth_price = coingecko_reader.get_price("eth")
    assert btc_price.price > 5000
    assert eth_price.price > 100
    assert btc_price.name == "btc"
    assert eth_price.name == "eth"



if __name__ == '__main__':
    pytest.main()