import datetime
import numpy as np
import pytest
import pandas as pd
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.fidelity.fidelity_constituents_report import generate_fidelity_constituents_report_df


def test_integration_jan_22() -> None:
    """
    This test tests that we can recreate the production file from jan 22. Pandas dataframe equals doesn't really work
    for this case so instead checks that calculations are the same
    """
    production_data_jan_22 = pd.read_csv("files/fidelity-constituents-2023-01-22.csv")
    actual_data = generate_fidelity_constituents_report_df(reporting_date=datetime.date(year=2023, month=1, day=22))
    calculation_columns = ["asset_price", "asset_return1d", "marketcap", "weight", "index_shares"]
    for col in calculation_columns:
        decimal_round = 2 if col == 'marketcap' else 4
        rounded_production_col = production_data_jan_22[col].apply(lambda item: np.round(item, decimals=decimal_round))
        rounded_actual_col = actual_data[col].apply(lambda item: np.round(item, decimals=decimal_round))
        print(f"Checking equality of columns: {col}")
        assert rounded_production_col.equals(rounded_actual_col)


def test_integration_jan_23() -> None:
    """
    This test tests that we can recreate the production file from jan 22. Pandas dataframe equals doesn't really work
    for this case so instead checks that calculations are the same
    """
    production_data_jan_22 = pd.read_csv("files/fidelity-constituents-2023-01-23.csv")
    actual_data = generate_fidelity_constituents_report_df(reporting_date=datetime.date(year=2023, month=1, day=23))
    calculation_columns = ["asset_price", "asset_return1d", "marketcap", "weight", "index_shares"]
    for col in calculation_columns:
        decimal_round = 2 if col == 'marketcap' else 4
        rounded_production_col = production_data_jan_22[col].apply(lambda item: np.round(item, decimals=decimal_round))
        rounded_actual_col = actual_data[col].apply(lambda item: np.round(item, decimals=decimal_round))
        print(f"Checking equality of columns: {col}")
        assert rounded_production_col.equals(rounded_actual_col)


if __name__ == '__main__':
    pytest.main()