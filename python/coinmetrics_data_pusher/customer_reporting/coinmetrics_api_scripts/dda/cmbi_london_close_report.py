import datetime
import os
from typing import List

import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

DDA_CMBI_ASSETS_LIST = ["CMBIBTC","CMBIETH","CMBISOL","CMBIAPE"]

client = CoinMetricsClientFactory.get_client()


def create_dda_cmbi_london_close_file(report_date: datetime.date, folder_location: str) -> str:
    """
    :param report_date: date for report to be generated
    :param folder_location: str path folder location
    :return: str path to the file. Filewill be named "cmbi_indexes_london_close_YYYY-MM-DD.csv"
    """
    data_df = get_index_prices_df_london_close(report_date=report_date, list_of_indexes=DDA_CMBI_ASSETS_LIST)
    full_folder_location = os.path.join(folder_location, f"cmbi_indexes_london_close_{report_date.strftime('%Y-%m-%d')}.csv")
    data_df.to_csv(full_folder_location, index=False)
    return full_folder_location


def get_index_prices_df_london_close(report_date: datetime.date,
                                     list_of_indexes: List[str]) -> pd.DataFrame:
    """
    Get a dataframe of index levels for london close for a specified number of indices. For the purpose of this report
    we are considering london close to be 4pm london time, rather than 4:30.
    :param report_date: date for report to be run
    :param list_of_indexes: indexes to get the prices for
    :return: dataframe with the price levels with the columns index, time, level
    """
    london_tz = pytz.timezone("Europe/London")
    four_pm_london = london_tz.localize(datetime.datetime(year=report_date.year,
                                                          month=report_date.month,
                                                          day=report_date.day, hour=16))
    four_pm_london_utc = four_pm_london.astimezone(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S')
    index_data = client.get_index_levels(indexes=list_of_indexes, frequency="1h",
                                         start_time=four_pm_london_utc, end_time=four_pm_london_utc).to_dataframe()
    index_data.rename(columns={"time": "time_utc"}, inplace=True)
    index_data['time_london'] = four_pm_london.strftime('%Y-%m-%dT%H:%M:%S')
    index_data = index_data[['index', 'time_london', 'time_utc', 'level']]
    return index_data


if __name__ == '__main__':
    create_dda_cmbi_london_close_file(report_date=datetime.date.today(), folder_location=".")