import pandas_market_calendars as mcal
from datetime import datetime

if __name__ == '__main__':
    nyse_days: mcal.MarketCalendar = mcal.get_calendar("NYSE")
    valid_nyse_days = list(map(lambda x: x.date().strftime("%Y-%m-%d"), list(nyse_days.valid_days(start_date=datetime.today(), end_date=datetime(year=2028, day=1, month=1)))))
    with open("nyse_trading_days.py", 'w') as f:
        for day in valid_nyse_days:
            f.write(f"{day}\n")