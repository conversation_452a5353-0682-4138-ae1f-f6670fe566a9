import os
from datetime import datetime, timed<PERSON>ta
from typing import Tuple, List

import ciso8601

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory
from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.retry_util import RetryUtil

log = LoggerFactory.get_logger("ExportAtlasData")

client = CoinMetricsClientFactory.get_client()


def generate_monthly_reference_rates_file(folder_location: str) -> str:
    """
    Wrapper around generate_reference_rates_file in order to provide the correct default arguments for end of month
    reporting
    :param folder_location: Location of the folder where the file will be generated
    :return: str name of the file, file will also be generated
    """
    assets_to_query = RetryUtil.retry_function(func=get_assets_with_reference_rates, max_retries=10, retry_delay=10)
    if assets_to_query is None:
        raise Exception('Cannot get assets to query')
    start, end = get_start_and_end_time_to_query()
    log.info('generating the reference rates file for month: %s', start.strftime('%B'))
    result_file_name = generate_reference_rates_file(assets_to_query, start, end, folder_location)
    return result_file_name


def generate_reference_rates_file(assets_to_query: List[str], start: datetime, end: datetime, folder_location: str, timezone: str="UTC") -> str:
    """
    Creates a file that has reference rates for each day in the month, ending on each day UTC time
    :param assets_to_query: list of string of assets i.e. ['btc', 'eth', 'ada']
    :param start: datetime start date for query inclusive
    :param end: datetime end date for query inclusive
    :param folder_location: str location on machine where the files should be stored
    :param timezone: timezone to use, defaults to UTC
    :return: str name of the file generated
    """
    reference_rates = client.get_asset_metrics(assets_to_query, 'ReferenceRate', frequency='1d', start_time=start,
                                               end_time=end, page_size=10000, timezone=timezone)
    lines = [f"{shift_day(rate['time'])},{rate['asset']},{rate['ReferenceRate']}" for rate in reference_rates]

    result_file_name = f'reference_rates_{start.strftime("%B").lower()}_{start.year}.csv'
    full_file_path = os.path.join(folder_location, result_file_name)
    with open(full_file_path, 'w') as reference_rates_file:
        reference_rates_file.write('rate_time,name,rate_final_price\n')
        reference_rates_file.write('\n'.join(sorted(lines)))

    return full_file_path


def get_assets_with_reference_rates() -> List[str]:
    """
    Helper function to get all CM assets with reference rates for 1d
    :return: List of str of assets with reference rates
    """
    metrics = client.catalog_metrics('ReferenceRate')
    for frequency_info in metrics[0]['frequencies']:
        if frequency_info['frequency'] == '1d':
            return frequency_info['assets']


def get_start_and_end_time_to_query() -> Tuple[datetime, datetime]:
    """
    Helper function to get the correct dates for query for the current month
    :return: Tuple of dates from start to end of the current month
    """
    today = datetime.now()

    if today.month == 1:
        year = today.year - 1
        month = 12
    else:
        year = today.year
        month = today.month - 1

    start = datetime(year, month, 2)
    end = datetime(today.year, today.month, 1)
    return start, end


def shift_day(date_str: str) -> str:
    return str(ciso8601.parse_datetime_as_naive(date_str) - timedelta(days=1))

