import datetime
import os

import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()

GRAYSCALE_REPORT_ASSETS = [
    "1inch",
    "aave",
    "ada",
    "aero",
    "akt",
    "algo",
    "amp",
    "apt",
    "ar",
    "atom",
    "avax",
    "axs",
    "baby",
    "bat",
    "bch",
    "bera",
    "bnt",
    "bnb",
    "btc",
    "chr",
    "comp",
    "crv",
    "deep",
    "doge",
    "dot",
    "eigen",
    "ena",
    "enj",
    "eos",
    "etc",
    "eth",
    "fet",
    "fil",
    "flow",
    "gala",
    "geod",
    "grass",
    "grt",
    "hbar",
    "hnt",
    "hype",
    "icp",
    "ip",
    "jto",
    "jup",
    "kava",
    "ldo",
    "link",
    "lpt",
    "lrc",
    "ltc",
    "mana",
    "matic",
    "mkr",
    "mnt",
    "move",
    "near",
    "nmr",
    "om",
    "ondo",
    "op",
    "pendle",
    "pengu",
    "plume",
    "ren",
    "render",
    "rndr",
    "rsr",
    "sand",
    "sky_sky",
    "snx",
    "sol",
    "stx",
    "sui",
    "sushi",
    "sxt",
    "tao_bittensor",
    "tia",
    "usds",
    "uma",
    "uni",
    "vet",
    "wal",
    "waxp",
    "wld",
    "xlm",
    "xmr",
    "xrp",
    "xtz",
    "yfi",
    "zec",
    "zen",
    "zrx",
]


def create_grayscale_report(report_date: datetime.date, folder_location: str) -> str:
    """
    Get's realtime reference rates at 4pm for the assets in the GRAYSCALE_ASSETS variable
    :param report_date: date of the report to be generated
    :param folder_location: str where the file should be created
    :return: file with the realtime reference rates for the assets in the format grayscale_assets_NY.csv
    """
    ny_tz = pytz.timezone("America/New_York")
    report_date_4pm = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                       month=report_date.month,
                                                       day=report_date.day,
                                                       hour=16)).astimezone(pytz.timezone("UTC"))
    one_minute_later = report_date_4pm + datetime.timedelta(minutes=1)
    date_string_format = "%Y-%m-%dT%H:%M:00"
    start_string = report_date_4pm.strftime(date_string_format)
    end_string = one_minute_later.strftime(date_string_format)
    rr_data: pd.DataFrame = client.get_asset_metrics(assets=GRAYSCALE_REPORT_ASSETS, metrics="ReferenceRateUSD",
                                       start_time=start_string, end_time=end_string, frequency="1m", end_inclusive=False).to_dataframe()
    rr_data['time'] = rr_data['time'].apply(lambda dat: dat.strftime('%Y-%m-%d %H:%M:%S'))
    full_file_name = os.path.join(folder_location, "CM_rates_NY.csv")
    rr_data.to_csv(full_file_name, index=False, header=False)
    return full_file_name


if __name__ == '__main__':
    create_grayscale_report(report_date=datetime.date.today() - datetime.timedelta(days=1), folder_location=".")
