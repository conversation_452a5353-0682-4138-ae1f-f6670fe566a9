import datetime
import os

import numpy as np
import pandas as pd
import pytz

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.coin_metrics_client_factory import \
    CoinMetricsClientFactory

client = CoinMetricsClientFactory.get_client()

BTC_TOTAL_RETURN_DATE = datetime.date(year=2017, month=11, day=1)
BTC_TOTAL_RETURN_MULTIPLIER = 1.07438833
ETH_TOTAL_RETURN_DATE = datetime.date(year=2017, month=3, day=1)
ETH_TOTAL_RETURN_MULTIPLIER = 1.08559236


def generate_cmbi_btc_close_file(report_date: datetime.date, output_directory: str) -> str:
    """
    Generates realtime and close files for CMBIBTC
    :param report_date: date for report, will get data for 4PM America/New_York
    :param output_directory: directory where files should go
    :return: List[str] name of the files generated
    """
    index = "CMBIBTC"
    ny_tz = pytz.timezone("America/New_York")
    report_date_4pm = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                       month=report_date.month,
                                                       day=report_date.day,
                                                       hour=16)).astimezone(pytz.timezone("UTC"))
    previous_date_4pm = report_date_4pm - datetime.timedelta(days=1)
    date_string_format = "%Y-%m-%dT%H:%M:00"
    data: pd.DataFrame = client.get_index_levels(indexes=index,
                                                 start_time=previous_date_4pm.strftime(date_string_format),
                                                 end_time=report_date_4pm.strftime(date_string_format),
                                                 frequency="1h", start_inclusive=False).to_dataframe()
    btc_total_return_constant = 1.07438833
    data['total_return'] = data['level'].apply(lambda lvl: np.round(np.multiply(lvl, btc_total_return_constant),
                                                                    decimals=10))
    data.drop("index", axis=1, inplace=True)
    data.rename(columns={"time": "cm_index_time", "level":"cm_index_price"}, inplace=True)
    data['cm_index_time'] = data['cm_index_time'].apply(lambda dat: dat.strftime('%Y-%m-%d %H:%M:%S'))
    report_name = f"cmbi-btc-close-{report_date.strftime('%Y-%m-%d')}T1600-0400.csv"
    full_file_name = os.path.join(output_directory, report_name)
    data.to_csv(full_file_name, index=False)
    return full_file_name


def generate_cmbi_eth_close(report_date: datetime.date, output_directory: str) -> str:
    """
    Generates realtime and close files for CMBIBTC
    :param report_date: date for report, will get data for 4PM America/New_York
    :param output_directory: directory where files should go
    :return: List[str] name of the files generated
    """
    index = "CMBIETH"
    ny_tz = pytz.timezone("America/New_York")
    report_date_4pm = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                       month=report_date.month,
                                                       day=report_date.day,
                                                       hour=16)).astimezone(pytz.timezone("UTC"))
    previous_date_4pm = report_date_4pm - datetime.timedelta(days=1)
    date_string_format = "%Y-%m-%dT%H:%M:00"
    data: pd.DataFrame = client.get_index_levels(indexes=index,
                                                 start_time=previous_date_4pm.strftime(date_string_format),
                                                 end_time=report_date_4pm.strftime(date_string_format),
                                                 frequency="1h", start_inclusive=False).to_dataframe()
    eth_total_return_constant = ETH_TOTAL_RETURN_MULTIPLIER
    data['total_return'] = data['level'].apply(lambda lvl: np.round(np.multiply(lvl, eth_total_return_constant),
                                                                    decimals=10))
    data.drop("index", axis=1, inplace=True)
    data.rename(columns={"time": "cm_index_time", "level": "cm_index_price"}, inplace=True)
    data['cm_index_time'] = data['cm_index_time'].apply(lambda dat: dat.strftime('%Y-%m-%d %H:%M:%S'))
    report_name = f"cmbi-eth-close-{report_date.strftime('%Y-%m-%d')}T1600-0400.csv"
    full_file_name = os.path.join(output_directory, report_name)
    data.to_csv(full_file_name, index=False)
    return full_file_name


def generate_cmbi_btc_file_rt(report_date: datetime.date, output_directory: str) -> str:
    """
    Generates realtime and close files for CMBIBTC
    :param report_date: date for report, will get data for 4PM America/New_York
    :param output_directory: directory where files should go
    :return: List[str] name of the files generated
    """
    index = "CMBIBTC"
    ny_tz = pytz.timezone("America/New_York")
    report_date_4pm = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                       month=report_date.month,
                                                       day=report_date.day,
                                                       hour=16)).astimezone(pytz.timezone("UTC"))
    previous_date_4pm = report_date_4pm - datetime.timedelta(days=1)
    date_string_format = "%Y-%m-%dT%H:%M:00"
    data: pd.DataFrame = client.get_index_levels(indexes=index,
                                                 start_time=previous_date_4pm.strftime(date_string_format),
                                                 end_time=report_date_4pm.strftime(date_string_format),
                                                 frequency="15s", start_inclusive=False).to_dataframe()
    btc_total_return_constant = BTC_TOTAL_RETURN_MULTIPLIER
    data['total_return'] = data['level'].apply(lambda lvl: np.round(np.multiply(lvl, btc_total_return_constant),
                                                                    decimals=10))
    data.drop("index", axis=1, inplace=True)
    data.rename(columns={"time": "cm_index_time", "level":"cm_index_price"}, inplace=True)
    data['cm_index_time'] = data['cm_index_time'].apply(lambda dat: dat.strftime('%Y-%m-%d %H:%M:%S'))
    report_name = f"cmbi-btc-rt-{report_date.strftime('%Y-%m-%d')}T1600-0400.csv"
    full_file_name = os.path.join(output_directory, report_name)
    data.to_csv(full_file_name, index=False)
    return full_file_name

def generate_cmbi_eth_file_rt(report_date: datetime.date, output_directory: str) -> str:
    """
    Generates realtime and close files for CMBIBTC
    :param report_date: date for report, will get data for 4PM America/New_York
    :param output_directory: directory where files should go
    :return: List[str] name of the files generated
    """
    index = "CMBIETH"
    ny_tz = pytz.timezone("America/New_York")
    report_date_4pm = ny_tz.localize(datetime.datetime(year=report_date.year,
                                                       month=report_date.month,
                                                       day=report_date.day,
                                                       hour=16)).astimezone(pytz.timezone("UTC"))
    previous_date_4pm = report_date_4pm - datetime.timedelta(days=1)
    date_string_format = "%Y-%m-%dT%H:%M:00"
    data: pd.DataFrame = client.get_index_levels(indexes=index,
                                                 start_time=previous_date_4pm.strftime(date_string_format),
                                                 end_time=report_date_4pm.strftime(date_string_format),
                                                 frequency="15s", start_inclusive=False).to_dataframe()
    eth_total_return_constant = ETH_TOTAL_RETURN_MULTIPLIER
    data['total_return'] = data['level'].apply(lambda lvl: np.round(np.multiply(lvl, eth_total_return_constant),
                                                                    decimals=10))
    data.drop("index", axis=1, inplace=True)
    data.rename(columns={"time": "cm_index_time", "level":"cm_index_price"}, inplace=True)
    data['cm_index_time'] = data['cm_index_time'].apply(lambda dat: dat.strftime('%Y-%m-%d %H:%M:%S'))
    report_name = f"cmbi-eth-rt-{report_date.strftime('%Y-%m-%d')}T1600-0400.csv"
    full_file_name = os.path.join(output_directory, report_name)
    data.to_csv(full_file_name, index=False)
    return full_file_name


