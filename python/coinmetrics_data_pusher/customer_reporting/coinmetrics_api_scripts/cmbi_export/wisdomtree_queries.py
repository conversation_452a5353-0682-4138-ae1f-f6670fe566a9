import os

SCHEMA = os.environ.get("PG_SCHEMA")

INDEX_VALUES_QUERY = f"select 'RTREE' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2001 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'RTREET' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2001 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WALTGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2002 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WALTGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2002 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WDFIGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2003 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WDFIGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2003 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WVRSGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2004 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WVRSGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_index_price as \"index_value\" \
from {SCHEMA}.cm_index_close where cm_index_id = 2004 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
order by 1,2 "


INDEX_WEIGHTS_QUERY = f"\
select 'RTREE' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2001 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'RTREET' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2001 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WALTGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2002 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WALTGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2002 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WDFIGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2003 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WDFIGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2003 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WVRSGM' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2004 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
UNION \
select 'WVRSGMT' as \"index\", \
cm_index_time as \"time_utc\", \
cm_index_time at time zone 'utc' at time zone 'america/new_york' as \"time_newyork\", \
cm_currency_ticker as \"asset\", \
cm_weight as \"index_weights\" \
from {SCHEMA}.cm_index_constituents where cm_index_id = 2004 \
and date_part('hour', cm_index_time at time zone 'utc' at time zone 'america/new_york') = 16 \
order by 1,2,3,4 "


WISDOMTREE_ASSETS = ["aave",
"ape",
"audio",
"avax",
"axs",
"bal",
"bat",
"btc",
"comp",
"crv",
"dot",
"enj",
"eth",
"fil",
"ftm",
"gala",
"grt",
"link",
"luna",
"mana",
"matic",
"mkr",
"qnt",
"ray",
"sand",
"snx",
"sol",
"sushi",
"uni",
"yfi",
]
