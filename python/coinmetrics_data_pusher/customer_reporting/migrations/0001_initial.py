# Generated by Django 4.1 on 2022-08-17 23:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('django_celery_beat', '0016_alter_crontabschedule_timezone'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=128)),
                ('sftp_hostname', models.CharField(blank=True, max_length=256)),
                ('sftp_username', models.CharField(blank=True, max_length=256)),
                ('sftp_password', models.CharField(blank=True, max_length=256)),
                ('sftp_port', models.IntegerField(blank=True, default=None, null=True)),
                ('sftp_path', models.Char<PERSON>ield(blank=True, default='', max_length=256, null=True)),
                ('email', models.Char<PERSON>ield(blank=True, max_length=256)),
                ('notes', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_method', models.CharField(blank=True, choices=[('email', 'email'), ('sftp', 'sftp')], max_length=32)),
                ('timestamp_created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('report_name', models.TextField(max_length=128)),
                ('notes', models.TextField(blank=True, default='', max_length=2480, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer_reporting.customer')),
                ('periodic_task', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='django_celery_beat.periodictask')),
            ],
        ),
        migrations.CreateModel(
            name='FriendlyScheduleName',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name_of_schedule', models.CharField(max_length=128)),
                ('clocked', models.ForeignKey(blank=True, help_text='Clocked Schedule to run the task on.  Set only one schedule type, leave the others null.', null=True, on_delete=django.db.models.deletion.CASCADE, to='django_celery_beat.clockedschedule', verbose_name='Clocked Schedule')),
                ('crontab', models.ForeignKey(blank=True, help_text='Crontab Schedule to run the task on.  Set only one schedule type, leave the others null.', null=True, on_delete=django.db.models.deletion.CASCADE, to='django_celery_beat.crontabschedule', verbose_name='Crontab Schedule')),
                ('interval', models.ForeignKey(blank=True, help_text='Interval Schedule to run the task on.  Set only one schedule type, leave the others null.', null=True, on_delete=django.db.models.deletion.CASCADE, to='django_celery_beat.intervalschedule', verbose_name='Interval Schedule')),
                ('solar', models.ForeignKey(blank=True, help_text='Solar Schedule to run the task on.  Set only one schedule type, leave the others null.', null=True, on_delete=django.db.models.deletion.CASCADE, to='django_celery_beat.solarschedule', verbose_name='Solar Schedule')),
            ],
        ),
        migrations.CreateModel(
            name='CustomerReportWorkload',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(default='CREATED', max_length=32)),
                ('timestamp_created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('start_time_task', models.DateTimeField(blank=True, default=None, null=True)),
                ('end_time_task', models.DateTimeField(blank=True, default=None, null=True)),
                ('failure_message', models.TextField(blank=True)),
                ('location_of_file_backup', models.CharField(blank=True, max_length=512)),
                ('files_delivered_successfully', models.IntegerField(blank=True, default=0, null=True)),
                ('files_delivered_failed', models.IntegerField(blank=True, default=0, null=True)),
                ('customer_report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer_reporting.customerreport')),
            ],
        ),
        migrations.AddField(
            model_name='customerreport',
            name='schedule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer_reporting.friendlyschedulename'),
        ),
    ]
