from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('customer_reporting', '0010_sftp_rsa_private_key_auth'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerreport',
            name='delivery_method',
            field=models.CharField(blank=True, choices=[("email", "email"),
                                                        ("sftp", "sftp"),
                                                        ("internal_sftp", "internal_sftp"),
                                                        ("sftp_private_key_auth", "sftp_private_key_auth"),
                                                        ("sftp_rsa_private_key_auth", "sftp_rsa_private_key_auth"),
                                                        ("minio_file_upload", "minio_file_upload"),
                                                        ("monitoring_task", "monitoring_task")], max_length=32),
        ),
    ]
