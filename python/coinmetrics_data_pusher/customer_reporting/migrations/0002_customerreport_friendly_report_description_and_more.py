# Generated by Django 4.1.1 on 2022-09-10 09:16

from django.db import migrations, models
import django_cryptography.fields


class Migration(migrations.Migration):

    dependencies = [
        ('customer_reporting', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customerreport',
            name='friendly_report_description',
            field=models.TextField(blank=True, default='', max_length=2480, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='sftp_password',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, max_length=256)),
        ),
    ]
