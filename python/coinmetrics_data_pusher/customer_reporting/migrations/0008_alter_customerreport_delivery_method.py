# Generated by Django 4.1.4 on 2023-04-14 13:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customer_reporting', '0007_customerreport_zip_report_files'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerreport',
            name='delivery_method',
            field=models.CharField(blank=True, choices=[('email', 'email'), ('sftp', 'sftp'), ('internal_sftp', 'internal_sftp'), ('sftp_private_key_auth', 'sftp_private_key_auth'), ('minio_file_upload', 'minio_file_upload')], max_length=32),
        ),
    ]
