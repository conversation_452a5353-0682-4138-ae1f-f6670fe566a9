from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('customer_reporting', '0009_customer_minio_bucket'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerreport',
            name='delivery_method',
            field=models.CharField(blank=True, choices=[("email", "email"),
                                                        ("sftp", "sftp"),
                                                        ("internal_sftp", "internal_sftp"),
                                                        ("sftp_private_key_auth", "sftp_private_key_auth"),
                                                        ("sftp_rsa_private_key_auth", "sftp_rsa_private_key_auth"),
                                                        ("minio_file_upload", "minio_file_upload")], max_length=32),
        ),
    ]
