import os
from minio import Minio
from datetime import datetime


def download_latest_backup_from_minio(environment: str) -> bytes:
    """
    This function will download the latest back up database file from the minion bucket for the specified environment
    """
    client = get_minio_client()
    minio_objects = client.list_objects(bucket_name="data-pusher-sqlite-backups")
    all_files = [file.object_name for file in minio_objects if file.object_name.startswith(environment)]
    all_files.sort(key=lambda file_name: datetime.strptime(file_name.split("_")[2].split(".")[0], "%Y-%m-%dT%H-%M-%S"))
    latest_backup_file = all_files[-1]
    print(f"Downloading: {latest_backup_file}")
    backup_file = client.get_object(bucket_name="data-pusher-sqlite-backups", object_name=latest_backup_file)
    return backup_file.read()


def replace_current_db_with_backup(environment: str) -> None:
    """
    This script will replace the current database with the most recent production backup. It's very important this script
    is only run when there has been a very recent production backup, otherwise there will be reports triggered out of
    order, potentially affecting clients.
    :param environment: either PRODUCTION or STAGING to indicate which environment the backup should come from
    """
    path_to_database = os.environ.get("CM_DATA_PUSHER_DB")
    if not path_to_database:
        raise AssertionError(f"Requires path to database")
    latest_prod_database = download_latest_backup_from_minio(environment)
    print(f"Replacing database at: {path_to_database} with latest prod backup")
    with open(path_to_database, 'wb') as db:
        db.write(latest_prod_database)


def download_specific_backup_file(minio_file_name: str) -> bytes:
    """
    This function will just download a specific backup file as bytes for more flexible use
    :param minio_file_name: the actual file name to download
    """
    client = get_minio_client()
    backup_file = client.get_object(bucket_name="data-pusher-sqlite-backups", object_name=minio_file_name)
    return backup_file.read()


def get_minio_client() -> Minio:
    """
    Get's a minio client with the configiration to download from our hel-1-hetz minio buckets
    """
    minio_access_key = os.environ.get("MINIO_ACCESS_KEY_ID")
    minio_secret_key = os.environ.get("MINIO_SECRET_ACCESS_KEY")
    if not all([minio_secret_key, minio_access_key]):
        raise AssertionError("MINIO_ACCESS_KEY_ID and MINIO_SECRET_ACCESS_KEY must set in env in "
                             "order to drop files to minio")
    minio_region = os.environ.get("MINIO_REGION", "eu-hel1-hetz")
    endpoint = os.environ.get("MINIO_URL", "minio.cnmtrcs.io:9002")
    if endpoint.startswith("https://"):
        endpoint = endpoint.split("//")[1]
    client = Minio(
        endpoint=endpoint,
        access_key=minio_access_key,
        secret_key=minio_secret_key,
        region=minio_region
    )
    return client
