import os
import sqlite3
import datetime


def create_db_backup(file_backup_location: str = "/tmp/data") -> str:
    """
    Creates a backup of the database in the format sqlite_YYYY-MM-DDTHH-MM-SS.db.backup
    :param file_backup_location: Location where the database backup file should be created

    """
    current_env = os.environ.get("ENVIRONMENT")
    source_db_path = os.environ.get('CM_DATA_PUSHER_DB')
    backup_file_name = f'{current_env}_sqlite_{datetime.datetime.now().strftime("%Y-%m-%dT%H-%M-%S")}.db.backup'
    temp_backup_path = os.path.join(file_backup_location, backup_file_name)

    source_conn = sqlite3.connect(source_db_path)
    backup_conn = sqlite3.connect(temp_backup_path)

    with backup_conn:
        source_conn.backup(backup_conn)

    source_conn.close()
    backup_conn.close()
    return temp_backup_path

