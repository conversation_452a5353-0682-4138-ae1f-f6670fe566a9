import datetime

import pytz
from croniter import croniter
from django.db import models
from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule, ClockedSchedule, SolarSchedule
from typing import Union, Dict
from datetime import datetime as dt
from django_cryptography.fields import encrypt

class FriendlyScheduleName(models.Model):
    """
    This class is to make it easier to set schedules by naming the different schedules so they can be easily assigned.
    For example, we can save a cron schedule as "Send on the first of every month" rather than 0 0 0 1 * *, which
    makes it easier to select a time from the UI, and without having so many different fields.
    """
    name_of_schedule = models.CharField(max_length=128)
    interval = models.ForeignKey(
        IntervalSchedule, on_delete=models.CASCADE,
        null=True, blank=True, verbose_name=('Interval Schedule'),
        help_text=('Interval Schedule to run the task on.  '
                   'Set only one schedule type, leave the others null.'),
    )
    crontab = models.ForeignKey(
        CrontabSchedule, on_delete=models.CASCADE, null=True, blank=True,
        verbose_name=('Crontab Schedule'),
        help_text=('Crontab Schedule to run the task on.  '
                   'Set only one schedule type, leave the others null.'),
    )
    solar = models.ForeignKey(
        SolarSchedule, on_delete=models.CASCADE, null=True, blank=True,
        verbose_name=('Solar Schedule'),
        help_text=('Solar Schedule to run the task on.  '
                   'Set only one schedule type, leave the others null.'),
    )
    clocked = models.ForeignKey(
        ClockedSchedule, on_delete=models.CASCADE, null=True, blank=True,
        verbose_name=('Clocked Schedule'),
        help_text=('Clocked Schedule to run the task on.  '
                   'Set only one schedule type, leave the others null.'),
    )

    def clean(self) -> None:
        """
        Overrides clean method to make sure that only one schedule can be provided. Clean method is mainly for data
        validation
        """
        list_schedules = []
        for sched in [self.clocked, self.solar, self.interval, self.crontab]:
            if sched is not None:
                list_schedules.append(list_schedules)
        if len(list_schedules) == 0:
            raise AssertionError("Must provide one schedule, 0 provided")
        if len(list_schedules) > 1:
            raise AssertionError(f"Must provide only one schedule, multiple provided: {list_schedules}")
        super().clean()

    def get_schedule(self) -> Dict[str, Union[ClockedSchedule, SolarSchedule, CrontabSchedule, IntervalSchedule]]:
        """
        Only one schedule will be set the rest will be null, this function returns whatever schedule is not null
        :return: A schedule of type Union[ClockedSchedule, SolarSchedule, CrontabSchedule, IntervalSchedule]:
        """
        schedules_and_types = zip(["solar", "crontab", "clocked", "interval"], [self.solar, self.crontab, self.clocked, self.interval])
        for schedule_type, sched in schedules_and_types:
            if sched is not None:
                return {schedule_type: sched}
        raise AssertionError("All schedules are null - invalid")

    def get_next_time_schedule_is_valid(self) -> datetime.datetime:
        crontab: CrontabSchedule = CrontabSchedule.objects.get(pk=self.crontab.id)
        cron_string = f"{crontab.minute} {crontab.hour} {crontab.day_of_week} {crontab.day_of_month} {crontab.month_of_year}"
        cron_iter = croniter(cron_string, datetime.datetime.now(tz=pytz.timezone(str(crontab.timezone))))
        next_date = cron_iter.get_next()
        return datetime.datetime.fromtimestamp(next_date, tz=pytz.UTC)

    def __str__(self):
        return self.name_of_schedule


class Customer(models.Model):
    """
    This class represents customer models in the database. Stores information on how to reach them mainly - for now
    SFTP or email
    """
    customer_name: str = models.CharField(max_length=128)
    sftp_hostname: str = models.CharField(max_length=256, blank=True)
    sftp_username: str = models.CharField(max_length=256, blank=True)
    sftp_password = encrypt(models.CharField(max_length=256, blank=True))
    sftp_port: int = models.IntegerField(blank=True, default=None, null=True)
    sftp_path: str = models.CharField(max_length=256, blank=True, null=True, default="")
    minio_bucket: str = models.CharField(max_length=512, blank=True, null=True, default="")
    email: str = models.CharField(max_length=2048, blank=True)
    notes: str = models.TextField(blank=True)

    def get_sftp_creds_and_options(self) -> Dict[str, str]:
        """
        Gets the SFTP creds for teh customer object
        :return: Returns SFTP creds as a Dict of strings, in a format that can be easily digested by pysftp.Connection
        object i.e.
        {
        "host": "..",
        "password": "...",
        "username": "..."
        }
        will only include 'port' if sftp_port is set for the object
        """
        results = {
            "host": self.sftp_hostname,
            "username": self.sftp_username,
            "password": self.sftp_password
        }
        if self.sftp_port is not None:
            results['port'] = self.sftp_port
        return results

    def __str__(self):
        return self.customer_name


REPORT_DELIVERY_METHODS = [("email", "email"),
                           ("sftp", "sftp"),
                           ("internal_sftp", "internal_sftp"),
                           ("sftp_private_key_auth", "sftp_private_key_auth"),
                           ("sftp_rsa_private_key_auth", "sftp_rsa_private_key_auth"),
                           ("minio_file_upload", "minio_file_upload"),
                           ("monitoring_task", "monitoring_task")]


class CustomerReport(models.Model):
    """
    This class represents customer reports. It serves as a thin layer over the django_celery_beat.PeriodicTask
    model so that we can use the periodic tasks and tie the business logic related to our customers.
    """
    customer: Customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    periodic_task: PeriodicTask = models.ForeignKey(PeriodicTask, on_delete=models.CASCADE, blank=True, null=True, default=None)
    delivery_method: str = models.CharField(max_length=32, choices=REPORT_DELIVERY_METHODS, blank=True)
    timestamp_created: datetime.datetime = models.DateTimeField(auto_now_add=True)
    updated: datetime.datetime = models.DateTimeField(auto_now=True)
    report_name: str = models.TextField(max_length=128)
    schedule: FriendlyScheduleName = models.ForeignKey(FriendlyScheduleName, on_delete=models.CASCADE)
    notes: str = models.TextField(max_length=2480, blank=True, null=True, default="")
    friendly_report_description: str = models.TextField(max_length=2480, blank=True, null=True, default="")
    zip_report_files: bool = models.BooleanField(blank=True, null=True, default=False)

    def save(
        self, *args, **kwargs
    ):
        """
        Overriding the save method so that when a CustomerReport object is created it will create a related
        PeriodicTask object. When a report is edited, so that the schedule or task changes, it will update the
        underlying periodic task associated.
        """
        super().save(*args, **kwargs)
        if self.periodic_task is None:
            name_of_main_reporting_task = "coinmetrics_data_pusher.customer_reporting.tasks.generate_and_send_report"
            schedule = self.schedule.get_schedule()
            schedule_object: FriendlyScheduleName = FriendlyScheduleName.objects.get(pk=self.schedule.id)
            start_time_task = schedule_object.get_next_time_schedule_is_valid()
            related_periodic_task = PeriodicTask(name=str(self), task=name_of_main_reporting_task, kwargs=f'{{"customer_report_id": {self.id} }}',
                                                 enabled=True,  start_time=start_time_task, **schedule)
            related_periodic_task.save()
            self.periodic_task = related_periodic_task
        else:
            periodic_task_object: PeriodicTask = PeriodicTask.objects.get(pk=self.periodic_task.id)
            next_start_time = self.schedule.get_next_time_schedule_is_valid()
            schedule = self.schedule.get_schedule()
            for schedule_type, schedule_argument in schedule.items():
                setattr(periodic_task_object, schedule_type, schedule_argument)
            setattr(periodic_task_object, "start_time", next_start_time)
            setattr(periodic_task_object, "name", str(self))
            periodic_task_object.save()
        super().save()

    def delete(self, using=None, keep_parents=False) -> None:
        """
        Overriding delete method so that is destroys the related PeriodicTask object as well
        """
        periodic_task = PeriodicTask.objects.get(pk=self.periodic_task.id)
        periodic_task.delete()
        super().delete(using, keep_parents)

    def __str__(self):
        return f"{self.id} Report: {self.report_name} for customer: {self.customer} to be delivered by {self.delivery_method}"


class CustomerReportWorkload(models.Model):
    """
    This class stores information related to reports that are run for customers, so that we can store information
    like jobs status (CREATED, IN PROGRESS, SUCCESS, FAIL), where the backups are stored, etc.
    """
    customer_report = models.ForeignKey(CustomerReport, on_delete=models.CASCADE)
    status = models.CharField(default="CREATED", max_length=32)
    timestamp_created = models.DateTimeField(auto_now_add = True)
    updated = models.DateTimeField(auto_now = True)
    start_time_task = models.DateTimeField(blank=True, default=None, null=True)
    end_time_task = models.DateTimeField(blank=True, default=None, null=True)
    failure_message = models.TextField(blank=True)
    location_of_file_backup = models.CharField(max_length=512, blank=True)
    files_delivered_successfully: int = models.IntegerField(default=0, blank=True, null=True)
    files_delivered_failed: int = models.IntegerField(default=0, blank=True, null=True)




