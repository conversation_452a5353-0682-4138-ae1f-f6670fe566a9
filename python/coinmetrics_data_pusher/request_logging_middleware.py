from django.utils.deprecation import MiddlewareMixin

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("RequestLoggingMiddleware")


class RequestLoggingMiddleware(MiddlewareMixin):

    def process_request(self, request):
        log.info('Request: %s', request)
        return None  # Allow the request to continue to the next middleware or view
