"""
Django settings for coinmetrics_data_pusher project.

Generated by 'django-admin startproject' using Django 4.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

import os
from pathlib import Path

from coinmetrics_data_pusher.customer_reporting.coinmetrics_api_scripts.util.logger_factory import LoggerFactory

log = LoggerFactory.get_logger("settings")

BASE_DIR = Path(__file__).resolve().parent.parent


SECRET_KEY = os.environ.get('CM_DATA_PUSHER_SECRET_KEY', 'abcdefg')
if os.environ.get("CM_DATA_PUSHER_SECRET_KEY_OVERRIDE") and os.environ.get("CM_DATA_PUSHER_SECRET_KEY_OVERRIDE") != "NA":
    SECRET_KEY = os.environ.get("CM_DATA_PUSHER_SECRET_KEY_OVERRIDE")
FIELD_ENCRYPTION_KEY = os.environ.get('CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY', 'abcdefg')
if os.environ.get("CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY_OVERRIDE") and os.environ.get('CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY') != "NA":
    FIELD_ENCRYPTION_KEY = os.environ.get("CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY_OVERRIDE")

if os.environ.get('CM_DATA_PUSHER_DEBUG') == "true":
    DEBUG = True
else:
    DEBUG = False

log.info(f"CM_DATA_PUSHER_DEBUG={os.environ.get('CM_DATA_PUSHER_DEBUG')}")
log.info(f"DEBUG={DEBUG}")

if 'CM_DATA_PUSHER_ALLOWED_HOSTS' in os.environ:
    ALLOWED_HOSTS = os.environ['CM_DATA_PUSHER_ALLOWED_HOSTS'].split(',')
else:
    ALLOWED_HOSTS = ['*']

log.info(f"ALLOWED_HOSTS={ALLOWED_HOSTS}")

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'coinmetrics_data_pusher.customer_reporting',
    'django_celery_beat',
    'django_prometheus'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django_prometheus.middleware.PrometheusBeforeMiddleware',
    # All your other middlewares go here, including the default
    # middlewares like SessionMiddleware, CommonMiddleware,
    # CsrfViewmiddleware, SecurityMiddleware, etc.
    'django_prometheus.middleware.PrometheusAfterMiddleware',
]

if DEBUG:
    log.info(f"adding RequestLoggingMiddleware")
    MIDDLEWARE.append('coinmetrics_data_pusher.request_logging_middleware.RequestLoggingMiddleware')

ROOT_URLCONF = 'coinmetrics_data_pusher.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, "customer_reporting/templates")],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'coinmetrics_data_pusher.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.environ.get('CM_DATA_PUSHER_DB', '/tmp/db.sqlite'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

STATIC_URL = 'static/'

STATIC_ROOT = os.environ.get('CM_DATA_PUSHER_STATIC_ROOT', '/tmp/static')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


CELERY_BROKER_URL = os.environ.get('CM_DATA_PUSHER_CELERY_BROKER_URL', None)
CELERY_RESULT_BACKEND = CELERY_BROKER_URL
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = "EST"
# CELERY_WORKER_SEND_TASK_EVENTS and CELERY_TASK_SEND_SENT_EVENT are mandatory for Celery Exporter
CELERY_WORKER_SEND_TASK_EVENTS = True
CELERY_TASK_SEND_SENT_EVENT = True
# CELERY_ENABLE_UTC = False

CM_API_KEY = os.environ.get('CM_API_KEY', None)

if 'CM_DATA_PUSHER_CSRF_TRUSTED_ORIGINS' in os.environ:
    CSRF_TRUSTED_ORIGINS = os.environ['CM_DATA_PUSHER_CSRF_TRUSTED_ORIGINS'].split(',')
else:
    CSRF_TRUSTED_ORIGINS = []

# Email will not work without these set, should be done outside of version control
os.environ['SENDGRID_EMAIL'] = os.environ.get('SENDGRID_EMAIL', '')
os.environ['SENDGRID_API_KEY'] = os.environ.get('SENDGRID_API_KEY', '')
