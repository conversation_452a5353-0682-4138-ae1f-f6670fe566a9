---
image:
  repository: registry.gitlab.com/coinmetrics/data-delivery/export/data-pusher
  pullPolicy: IfNotPresent
  tag: ""

nameOverride: ""
fullnameOverride: ""

dataPusher:
  oauthBaseRedirectUri: ""
  oauthClientSecret: ""
  oauthClientId: ""
  replicaCount: ""
  environment: ""
  debug: false
  database: /opt/coinmetrics/data-pusher/db.sqlite
  settingsModule: coinmetrics_data_pusher.settings
  pgPassword: ""
  fieldEncryptionKey: ""
  cookieSecretKey: ""
  sendgridEmail: "<EMAIL>"
  sendgridApiKey: ""
  coinmetricsApiKey: ""
  coinmetricsApiBaseUrl: https://api.coinmetrics.io/v4
  coinmetricsSFTPKey: ""
  coinmetricsSftpRsaKey: ""
  minioAccessKeyID: ""
  minioSecretAccessKey: ""
  googleClientID: ""
  googleClientSecret: ""
  googleRefreshToken: ""
  resources:
    requests:
      cpu: "0.1"
      memory: ".5Gi"
    limits:
      memory: ".5Gi"
  persistence:
    storageClassName: ""
    size: ""
  tolerations: []

redis:
  replicaCount: ""
  create: true
  image:
    repository: redis
    tag: latest
    pullPolicy: IfNotPresent
  resources:
    requests:
      cpu: "0.1"
      memory: ".5Gi"
    limits:
      memory: ".5Gi"
  host: redis
  port: 6379
  username: default
  password: ""
  persistence:
    storageClassName: ""
    size: ""
  tolerations: []

celery:
  beat:
    create: true
    resources: {}
  worker:
    create: true
    resources:
      requests:
        cpu: "0.25"
        memory: "3Gi"
      limits:
        memory: "3Gi"

virtualServer:
  domain: ""
  ingressClassName: ""
