apiVersion: v1
kind: Secret
metadata:
  name: {{ include "data-pusher.fullname" . }}
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
data:
  redisUsername: {{ .Values.redis.username | b64enc }}
  redisPassword: {{ .Values.redis.password | b64enc }}
  dataPusherCookieSecretKey: {{ .Values.dataPusher.cookieSecretKey | b64enc }}
  dataPusherFieldEncryptionKey: {{ .Values.dataPusher.fieldEncryptionKey | b64enc }}
  pgPassword: {{ .Values.dataPusher.pgPassword | b64enc }}
  sendgridApiKey: {{ .Values.dataPusher.sendgridApiKey | b64enc }}
  coinmetricsApiKey: {{ .Values.dataPusher.coinmetricsApiKey | b64enc }}
  coinmetricsSFTPKey: {{ .Values.dataPusher.coinmetricsSFTPKey | b64enc }}
  coinmetricsSftpRsaKey: {{ .Values.dataPusher.coinmetricsSftpRsaKey | b64enc }}
  minioAccessKeyID:  {{ .Values.dataPusher.minioAccessKeyID | b64enc }}
  minioSecretAccessKey: {{ .Values.dataPusher.minioSecretAccessKey | b64enc }}
  googleClientID: {{ .Values.dataPusher.googleClientID | b64enc }}
  googleClientSecret: {{ .Values.dataPusher.googleClientSecret | b64enc }}
  googleRefreshToken: {{ .Values.dataPusher.googleRefreshToken | b64enc }}
  dataPusherCookieSecretKeyOverride: {{ .Values.dataPusher.dataPusherCookieSecretKeyOverride | b64enc }}
  dataPusherCookieFieldEncryptionKeyOverride: {{ .Values.dataPusher.dataPusherCookieFieldEncryptionKeyOverride | b64enc }}
  oauthClientId: {{ .Values.dataPusher.oauthClientId | b64enc }}
  oauthClientSecret: {{ .Values.dataPusher.oauthClientSecret | b64enc }}
type: Opaque
