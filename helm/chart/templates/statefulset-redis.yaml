{{- if .Values.redis.create }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "data-pusher.fullname" . }}-redis
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
    app.kubernetes.io/component: redis
spec:
  replicas: {{ .Values.redis.replicaCount }}
  selector:
    matchLabels:
      {{- include "data-pusher.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: redis
  serviceName: data-pusher
  template:
    metadata:
      labels:
        {{- include "data-pusher.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: redis
    spec:
      {{- with .Values.redis.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        {{- with .Values.redis.nodeAffinity }}
        nodeAffinity:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      containers:
        - args:
            - /etc/redis/redis.conf
          image: "{{ .Values.redis.image.repository }}:{{ .Values.redis.image.tag }}"
          imagePullPolicy: {{ .Values.redis.image.pullPolicy }}
          name: redis
          ports:
            - containerPort: {{ .Values.redis.port }}
              name: redis
              protocol: TCP
          resources:
            {{- toYaml .Values.redis.resources | nindent 12 }}
          volumeMounts:
            - name: state
              mountPath: /opt/coinmetrics/redis
            - name: config
              mountPath: /etc/redis
      terminationGracePeriodSeconds: 30
      volumes:
        - name: config
          projected:
            sources:
              - configMap:
                  name: {{ include "data-pusher.fullname" . }}-redis
                  optional: false
              - secret:
                  name: {{ include "data-pusher.fullname" . }}-redis
                  optional: false
                  items:
                    - key: users.acl
                      path: users.acl
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: state
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: {{ .Values.redis.persistence.size }}
        storageClassName: {{ .Values.redis.persistence.storageClassName }}
        volumeMode: Filesystem
{{- end }}
