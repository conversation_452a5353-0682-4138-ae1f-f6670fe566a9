apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ include "data-pusher.fullname" . }}-tls
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
spec:
  dnsNames:
    - "{{ .Values.virtualServer.domain }}"
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-prod
  secretName: {{ include "data-pusher.fullname" . }}-tls
  privateKey:
    rotationPolicy: Always
    algorithm: ECDSA
    size: 384
  revisionHistoryLimit: 2
  usages:
    - digital signature
    - key encipherment
