apiVersion: v1
kind: Service
metadata:
  name: {{ include "data-pusher.fullname" . }}
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
    app.kubernetes.io/component: gunicorn
spec:
  ipFamilies:
    - IPv6
  ipFamilyPolicy: SingleStack
  ports:
    - name: http
      port: 8000
      protocol: TCP
      targetPort: http
  selector:
    {{- include "data-pusher.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: gunicorn
  type: ClusterIP
