apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "data-pusher.fullname" . }}
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
spec:
  ingressClassName: "{{ .Values.virtualServer.ingressClassName }}"
  rules:
    - host: "{{ .Values.virtualServer.domain }}"
      http:
        paths:
          - backend:
              service:
                name: {{ include "data-pusher.fullname" . }}
                port:
                  name: http
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - "{{ .Values.virtualServer.domain }}"
      secretName: {{ include "data-pusher.fullname" . }}-tls
