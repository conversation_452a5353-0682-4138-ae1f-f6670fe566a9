{{- if .Values.redis.create }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "data-pusher.fullname" . }}-redis
  labels:
    {{- include "data-pusher.labels" . | nindent 4 }}
    app.kubernetes.io/component: redis
spec:
  ipFamilies:
    - IPv6
  ipFamilyPolicy: SingleStack
  ports:
    - name: redis
      port: {{ .Values.redis.port }}
      protocol: TCP
      targetPort: redis
  selector:
    {{- include "data-pusher.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: redis
  type: ClusterIP
{{- end }}
