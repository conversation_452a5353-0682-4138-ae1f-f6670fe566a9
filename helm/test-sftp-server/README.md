# Local environment

For local development:
```commandline
docker-compose up -d
```

## Testing

Test using a password:
```commandline
sftp -P 2222 sftpuser@localhost
```

For private key testing, download the private key from https://gitlab.com/coinmetrics/ops/operations/-/tree/master/roles/fidelity_export/files/client?ref_type=heads and use the following command:
```commandline
sftp -P 2222 -o IdentityFile=./coinmetrics2bloomberg -o StrictHostKeyChecking=accept-new sftpuser@localhost
```

# Staging environment

For staging environment setup:
```commandline
kubectl apply -f test-sftp-authorized-keys.yaml

kubectl apply -f test-sftp-server.yaml

kubectl apply -f test-sftp-service.yaml
```

For CP1, run additionally:
```commandline
kubectl --context cp1 apply -f test-sftp-policy.yaml
```

For Copper/CP1, add the following egress rule to the data-pusher network policy prior to the first deny rule.

Refer to https://gitlab.com/coinmetrics/ops/copper/-/blob/master/kubernetes/resources/policies/application/data-pusher.yaml

```yaml
    - action: Allow
      protocol: TCP
      source: {}
      destination:
        services:
          name: sftp-service
          namespace: data-pusher
```

Apply the rule:
```commandline
kubectl apply -f data-pusher.yaml
```

## Testing

Test using a password:
```commandline
sftp <EMAIL>
```

For private key testing, deploy a [python-debug-pod](..%2Fpython-debug-pod) pod.

Download the private key from https://gitlab.com/coinmetrics/ops/operations/-/tree/master/roles/fidelity_export/files/client?ref_type=heads, and copy it to the pod:

```commandline
kubectl -n data-pusher cp coinmetrics2bloomberg python-debug-pod:/
```

Finally, test from the python debug pod using the private key:
```commandline
kubectl -n data-pusher exec -it python-debug-pod -- bash

sftp -o IdentityFile=/coinmetrics2bloomberg -o StrictHostKeyChecking=accept-new <EMAIL>
```

Alternatively, you can use port-forwarding to your laptop for private key testing.
