apiVersion: v1
kind: Pod
metadata:
  name: sftp-pod
  namespace: data-pusher
  labels:
    app.kubernetes.io/component: test-sftp-server
    app.kubernetes.io/instance: data-pusher
    app.kubernetes.io/name: data-pusher
spec:
  containers:
  - name: sftp
    image: atmoz/sftp
    ports:
    - containerPort: 22
    env:
    - name: SFTP_USERS
      value: "sftpuser:pass:::upload"
    securityContext:
      runAsUser: 0  # Running as root
    volumeMounts:
    - name: data-volume
      mountPath: /home/<USER>/upload
    - name: authorized-keys
      mountPath: /home/<USER>/.ssh/keys
      readOnly: true
  volumes:
  - name: data-volume
    emptyDir: {}
  - name: authorized-keys
    configMap:
      name: sftp-authorized-keys
      items:
      - key: id_rsa.pub
        path: id_rsa.pub
