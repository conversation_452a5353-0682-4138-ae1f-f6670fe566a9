apiVersion: v1
kind: Service
metadata:
  name: sftp-service
  namespace: data-pusher
  labels:
    app.kubernetes.io/component: test-sftp-server
    app.kubernetes.io/instance: data-pusher
    app.kubernetes.io/name: data-pusher
spec:
  selector:
    app.kubernetes.io/component: test-sftp-server
    app.kubernetes.io/instance: data-pusher
    app.kubernetes.io/name: data-pusher
  ports:
    - name: sftp
      protocol: TCP
      port: 22
      targetPort: 22
