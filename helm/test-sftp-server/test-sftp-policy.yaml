apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: sftp-policy
  namespace: data-pusher
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/component: test-sftp-server
      app.kubernetes.io/instance: data-pusher
      app.kubernetes.io/name: data-pusher
  egress:
  - toEntities:
    - all
  ingress:
  - fromEndpoints:
    - matchLabels:
        app.kubernetes.io/instance: data-pusher
        app.kubernetes.io/name: data-pusher
    toPorts:
    - ports:
      - port: "22"
        protocol: TCP
