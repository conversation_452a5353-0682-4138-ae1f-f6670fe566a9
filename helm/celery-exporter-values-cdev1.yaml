env:
    - name: REDIS_USERNAME
      valueFrom:
        secretKeyRef:
          name: data-pusher-celery-exporter
          key: redisUsername
          optional: false
    - name: REDIS_PASSWORD
      valueFrom:
        secretKeyRef:
          name: data-pusher-celery-exporter
          key: redisPassword
          optional: false
    - name: "CE_HOST"
      value: "[::]"
    - name: "CE_BROKER_URL"
      value: redis://$(REDIS_USERNAME):$(REDIS_PASSWORD)@data-pusher-redis:6379
    - name: "CE_RETRY_INTERVAL"
      value: "5"
    - name: "CE_BUCKETS"
      value: "1,2.5,5,10,30,60,300,600,900,1800"
podAnnotations:
  prometheus.io/scrape: "true"
serviceMonitor:
  enabled: true

resources:
   limits:
     cpu: 250m
     memory: 256Mi
   requests:
     cpu: 250m
     memory: 256Mi
