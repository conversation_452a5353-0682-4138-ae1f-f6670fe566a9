ENVIRONMENT: LOCAL
CM_SFTP_SERVER: sftp.coinmetrics.io
CM_SFTP_USERNAME: data-pusher.flat.files
CM_SFTP_PORT: "2222"
CM_SFTP_DATA_ROOT: /opt/sftp-server-data/
SENDGRID_API_KEY: *********************************************************************
SENDGRID_EMAIL: <EMAIL>
PGPASSWORD: uTx0123
PG_TRADES_DB: pg-trades-spot-stg-hel1-p
PG_SCHEMA: staging
PGPORT: "5432"
PGUSER: postgres
PGHOST: localhost
PG_INDEX_DB: pg-indices-1
CM_API_KEY: 2xEyXpIBsjyATfAuGk3u
CM_API_BASE_URL: https://api.coinmetrics.io/v4
CM_SFTP_PRIVATE_KEY: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
CM_SFTP_RSA_PRIVATE_KEY: 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
AWS_ACCESS_KEY_ID: cm-data-pusher
AWS_SECRET_ACCESS_KEY: YjhhYThiMTI5MTI2MDNjMDFhMGNkYjY4
S3_BACKUP_BUCKET: data-pusher-sqlite-backups
S3_TARGET_URL: https://minio.cnmtrcs.io:9002
DATA_PUSHER_DB: /tmp/cm-data-pusher/db.sqlite
MINIO_ACCESS_KEY_ID: cm-data-pusher
MINIO_BUCKET: data-pusher-sqlite-backups
MINIO_SECRET_ACCESS_KEY: YjhhYThiMTI5MTI2MDNjMDFhMGNkYjY4
MINIO_URL: https://minio.cnmtrcs.io:9002
CM_DATA_PUSHER_DB: /tmp/cm-data-pusher/db.sqlite
GOOGLE_CLIENT_ID: 962118171407-6m5gpqnjjboghtc6dvkmufa0nbuqkicb.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET: 9UJJThPw5B_ZYvo_SQ8aJ7K0
GOOGLE_REFRESH_TOKEN: 1//04iC7fZ0KeolfCgYIARAAGAQSNwF-L9Ir6QtOoR1hkiXzGXewewKs0pu-AGifx79fCx9xI8eYQSCs5WJMiT28nme_sDcgISKtBGg
CM_DATA_PUSHER_SECRET_KEY: gPZmZvSJ9EeqFFSJ7nMiPK6wkHDsGs8b2i5aFE3oDXk=
CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY: mHJJRHOerSQxzKNIVmPo1elGYGymRYKXwO7+YmMYSl4=
CM_DATA_PUSHER_SECRET_KEY_OVERRIDE: qLnsszK2hyKtAlR9wje39TZ+Kran38U1XLC5wz7zo38=
CM_DATA_PUSHER_FIELD_ENCRYPTION_KEY_OVERRIDE: eAoZPyAEt29X8y-BhBf2fJXOUpuUIjhiH2_5TliqNDI=
OAUTH_CLIENT_ID: 361833837720-0dh19qclrshs5tf0t41i6rde88rb9lq9.apps.googleusercontent.com
OAUTH_CLIENT_SECRET: 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
OAUTH_BASE_REDIRECT_URI: http://127.0.0.1:8000