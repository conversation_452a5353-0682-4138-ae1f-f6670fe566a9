# Default values for celery-exporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: danihodovic/celery-exporter
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

env: []
  # - name: CE_BROKER_URL
  #   value: <MY_BROKER_URL>
  # - name: CE_BROKER_URL
  #   valueFrom:
  #     secretKeyRef:
  #       name: MY_SECRET
  #       key: MY_SECRET_KEY

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 9808

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: celery-exporter.example
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

serviceMonitor:
  enabled: false
  additionalLabels: {}
  ## The label to use to retrieve the job name from.
  ## jobLabel: "app.kubernetes.io/name"
  namespace: ""
  namespaceSelector: {}
  ## Default: scrape .Release.Namespace only
  ## To scrape all, use the following:
  ## namespaceSelector:
  ##   any: true
  scrapeInterval: 30s
  # honorLabels: true
  targetLabels: []
  relabelings: []
  metricRelabelings: []

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

livenessProbe: {}
  # Liveness and readiness probe timeout values.
  # timeoutSeconds: 5
  # failureThreshold: 5
  # periodSeconds: 10
  # successThreshold: 1
readinessProbe: {}
  # timeoutSeconds: 15
  # failureThreshold: 5
  # periodSeconds: 10
  # successThreshold: 1

nodeSelector: {}

tolerations: []

affinity: {}
