{{- if .Values.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "celery-exporter.fullname" . }}
{{- if .Values.serviceMonitor.namespace }}
  namespace: {{ .Values.serviceMonitor.namespace | quote }}
{{- end }}
  labels:
    {{- include "celery-exporter.labels" . | nindent 4 }}
  {{- if .Values.serviceMonitor.additionalLabels }}
    {{- toYaml .Values.serviceMonitor.additionalLabels | nindent 4 }}
  {{- end }}
spec:
  endpoints:
    - port: http
      interval: {{ .Values.serviceMonitor.scrapeInterval }}
    {{- if .Values.serviceMonitor.honorLabels }}
      honorLabels: true
    {{- end }}
    {{- if .Values.serviceMonitor.relabelings }}
      relabelings: {{ toYaml .Values.serviceMonitor.relabelings | nindent 8 }}
    {{- end }}
    {{- if .Values.serviceMonitor.metricRelabelings }}
      metricRelabelings: {{ toYaml .Values.serviceMonitor.metricRelabelings | nindent 8 }}
    {{- end }}
{{- if .Values.serviceMonitor.jobLabel }}
  jobLabel: {{ .Values.serviceMonitor.jobLabel | quote }}
{{- end }}
{{- if .Values.serviceMonitor.namespaceSelector }}
  namespaceSelector: {{ toYaml .Values.serviceMonitor.namespaceSelector | nindent 4 }}
{{- else }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}
{{- if .Values.serviceMonitor.targetLabels }}
  targetLabels:
  {{- range .Values.serviceMonitor.targetLabels }}
    - {{ . }}
  {{- end }}
{{- end }}
  selector:
    matchLabels:
      {{- include "celery-exporter.selectorLabels" . | nindent 6 }}
{{- end }}
