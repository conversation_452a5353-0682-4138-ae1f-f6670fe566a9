apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "celery-exporter.fullname" . }}
  labels:
    {{- include "celery-exporter.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "celery-exporter.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "celery-exporter.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "celery-exporter.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 9808
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /health
              port: http
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds | default "5" }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold | default "5" }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds | default "10" }}
            successThreshold: {{ .Values.livenessProbe.successThreshold | default "1" }}
          livenessProbe:
            httpGet:
              path: /health
              port: http
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds | default "5" }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold | default "5" }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds | default "10" }}
            successThreshold: {{ .Values.livenessProbe.successThreshold | default "1" }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
