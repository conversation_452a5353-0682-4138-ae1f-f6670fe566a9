{
  description = "Coin Metrics Data Pusher";

  inputs = {
    nixpkgs = {
      url = "github:NixOS/nixpkgs";
    };

    # The version of the API client in nixpkgs usually lags by a few days,
    # and it is not a process over which we have full control. Therefore,
    # we bypass nixpkgs and depend on the api-client-python flake directly.
    # Note that this repository is private, which means that the developer
    # must supply a suitable GitLab Personal Access Token (PAT).
    # See https://nixos.org/manual/nix/stable/command-ref/conf-file.html#conf-access-tokens
    api-client = {
      type = "gitlab";
      owner = "coinmetrics%2Fdata-delivery";
      repo = "api-client-python";
      # This is the version 2024.12.11.19.
      # To upgrade to the next version, we need to fix the usage of datetime objects that represent dates without timestamps.
      # This version of the Python API Client passes date values such as "2025-03-24" to the REST API as "2025-03-24".
      # The next version begins sending "2025-03-24T00:00:00", impacting our requests' results.
      rev = "34128a541a2f2c2f07b14fa72a0abcc739f6c8a0";
      inputs.nixpkgs.follows = "nixpkgs";
    };
  };

  outputs = { self, nixpkgs, api-client, flake-utils }:
  {
    overlays = import ./nix/overlays.nix;
  } // flake-utils.lib.eachDefaultSystem (system:
    let
      pkgs = import nixpkgs {
        inherit system;
        overlays = [
          self.overlays.all
        ] ++ nixpkgs.lib.optional (nixpkgs.lib.hasSuffix "-darwin" system) self.overlays.darwin;
      };

      deployApps = pkgs.callPackage ./nix/deploy.nix {
        image = self.packages.${system}.cm-data-pusher-image;
      };
    in {
      packages = rec {
        cm-data-pusher = pkgs.python311Packages.callPackage ./nix {
          coinmetrics-api-client = api-client.packages.${system}.coinmetrics-api-client-py311.overridePythonAttrs (_: {
            doCheck = false;
            doInstallCheck = false;
            nativeCheckInputs = [];
            checkInputs = [];
            preCheck = "";
            pytestCheckPhase = "";
            pythonImportsCheckPhase = "";
            pythonImportsCheck = [];
          });
        };

        cm-data-pusher-image = pkgs.callPackage ./nix/image.nix {
          inherit cm-data-pusher;
        };

        default = cm-data-pusher-image;

        tests = pkgs.callPackage ./nix/test.nix {
          inherit cm-data-pusher;
          python = pkgs.python311;
        };
      };

      apps = rec {
        login = {
          type = "app";
          program = "${deployApps.registryLoginScript}/bin/authenticate-to-registry";
        };

        publish = rec {
          type = "app";
          program = "${deployApps.publishImage}/bin/publish-image";
        };

        deploy = {
          type = "app";
          program = "${deployApps.deployApplication}/bin/deploy-application";
        };

        integrationTests = {
          type = "app";
          program = "${self.packages.${system}.tests.integrationTests}/bin/integration-tests";
        };
      };

      devShells = {
        default = pkgs.callPackage ./nix/shell.nix {
          inherit (self.packages.${system}) cm-data-pusher;
        };
      };
    }
  );
}
